import React from 'react';
import { Popconfirm } from 'antd';
import PRZButton from '@Components/Common/UI/PRZButton';
import PRZConfirmationPopover from '@Components/Common/UI/PRZConfirmationPopover';
import PRZText from '@Components/Common/UI/PRZText';
import PRZInput from '@Components/Common/UI/PRZInput';
import Helpers from '@Apis/helpers';
import { useExpenseForm } from '../ExpenseFormContext';
import { cdnUrl } from '@Utils/cdnHelper';

const FormFooter = ({ expense, onSaveDraft, onSaveAndIssue }) => {
  const {
    updateDocumentReason,
    buttonClick,
    createExpenseLoading,
    updateExpenseLoading,
    setFormState,
    user,
    selectedExpense,
  } = useExpenseForm();

  const hasCreatePermission = Helpers.getPermission(
    Helpers.permissionEntities.EXPENSES,
    Helpers.permissionTypes.CREATE,
    user
  );

  if (!hasCreatePermission) return null;

  if (selectedExpense) {
    return (
      <>
        <PRZConfirmationPopover
          title="Are you sure you want to update?"
          content={
            <>
              <PRZText text="Reason" required />
              <PRZInput
                placeholder="Enter update reason"
                value={updateDocumentReason}
                onChange={(e) =>
                  setFormState({
                    updateDocumentReason: e.target.value,
                  })
                }
              />
            </>
          }
          onConfirm={() => {
            if (!createExpenseLoading && !updateExpenseLoading) {
              onSaveDraft();
            }
          }}
          confirmButtonText="Confirm"
          cancelButtonText="Back"
          confirmDisabled={!updateDocumentReason || !expense}
        >
          <PRZButton
            id="save-as-draft"
            type="default"
            wrapperStyle={{ marginRight: '10px' }}
            buttonStyle={{
              width: '130px',
              height: '40px',
              border: '1px solid #2d7df7',
              color: '#2d7df7',
            }}
            isLoading={(createExpenseLoading || updateExpenseLoading) && buttonClick === 'DRAFT'}
            disabled={createExpenseLoading || updateExpenseLoading || !expense}
          >
            Save as Draft
          </PRZButton>
        </PRZConfirmationPopover>

        <PRZConfirmationPopover
          title="Are you sure you want to update?"
          content={
            <>
              <PRZText text="Reason" required />
              <PRZInput
                placeholder="Enter update reason"
                value={updateDocumentReason}
                onChange={(e) =>
                  setFormState({
                    updateDocumentReason: e.target.value,
                  })
                }
              />
            </>
          }
          onConfirm={() => {
            if (!createExpenseLoading && !updateExpenseLoading) {
              onSaveAndIssue();
            }
          }}
          confirmButtonText="Confirm"
          cancelButtonText="Back"
          confirmDisabled={!updateDocumentReason || !expense}
        >
          <PRZButton
            id="save-and-issue"
            isLoading={(createExpenseLoading || updateExpenseLoading) && buttonClick === 'APPROVE'}
            disabled={createExpenseLoading || updateExpenseLoading || !expense}
            buttonStyle={{ width: '130px', height: '40px' }}
          >
            Save and Issue
          </PRZButton>
        </PRZConfirmationPopover>
      </>
    );
  }

  return (
    <>
      <PRZButton
        id="save-as-draft"
        type="default"
        onClick={() => {
          if (!createExpenseLoading && !updateExpenseLoading) {
            onSaveDraft();
          }
        }}
        isLoading={(createExpenseLoading || updateExpenseLoading) && buttonClick === 'DRAFT'}
        disabled={createExpenseLoading || updateExpenseLoading || !expense}
        wrapperStyle={{ marginRight: '10px' }}
        buttonStyle={{
          width: '130px',
          height: '40px',
          border: '1px solid #2d7df7',
          color: '#2d7df7',
        }}
      >
        Save as Draft
      </PRZButton>

      <PRZButton
        id="save-and-issue"
        onClick={() => {
          if (!createExpenseLoading && !updateExpenseLoading) {
            onSaveAndIssue();
          }
        }}
        isLoading={(createExpenseLoading || updateExpenseLoading) && buttonClick === 'APPROVE'}
        disabled={createExpenseLoading || updateExpenseLoading || !expense}
        buttonStyle={{ width: '130px', height: '40px' }}
      >
        Save and Issue
      </PRZButton>

      {!expense && (
        <Popconfirm
          placement="topRight"
          title="This feature is not accessible within your current plan to use this feature contact us."
          onConfirm={() => globalThis.Intercom('showNewMessage')}
          okText="Contact Us"
          cancelText="Cancel"
        >
          <img
            className="barcode-restrict"
            src={cdnUrl("crown2.png", "images")}
            alt="premium"
            style={{
              marginLeft: '8px',
              marginTop: '5px',
            }}
          />
        </Popconfirm>
      )}
    </>
  );
};

export default FormFooter;
