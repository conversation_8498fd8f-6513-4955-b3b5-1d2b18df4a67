import Helpers from '@Apis/helpers';
import BarcodeReader from '@Components/Common/BarcodeReader';
import { cdnUrl } from '@Utils/cdnHelper';
import { Alert, Button, Popconfirm } from 'antd';
import React, { memo } from 'react';
import { getFooterMessage } from '../helper';
import { v4 as uuidv4 } from 'uuid';

function AdjustmentFormFooter({
  user,
  createIndentLoading,
  isBulkUpload,
  selectedAdjustmentType,
  data,
  selectedJobWork,
  userTenantDepartmentId,
  barCoding,
  handleCreateIndent,
  handleProductChange,
  onSearchBarcodeReader,
}) {
  const userMenuConfig = JSON.parse(localStorage.getItem('user_menu_config'));
  return (
    <div className={`create-indent__footer form__footer ${userMenuConfig === 'FIXED' ? 'form__footer-fixed' : ''}`}>
      {Helpers.getPermission(Helpers.permissionEntities.INVENTORY_INDENT,
        Helpers.permissionTypes.CREATE, user) && (
        <Button
          type="primary"
          onClick={() => {
            if (!createIndentLoading) {
              handleCreateIndent(true);
            }
          }}
          loading={createIndentLoading}
          disabled={createIndentLoading}
        >
            Save Adjustment
        </Button>
      )}
      {isBulkUpload && (
        <Alert
          className="create-indent__footer-alert"
          showIcon
          type="info"
          message={getFooterMessage(selectedAdjustmentType)}
        />
      )}

      <div className="indent-barcode__wrapper" style={{ display: 'flex' }}>
        {!barCoding && (
          <Popconfirm
            placement="topRight"
            title="This feature is not accessible within your current plan to use this feature contact us."
            onConfirm={() => globalThis.Intercom('showNewMessage')}
            okText="Contact Us"
            cancelText="Cancel"
          >
            <img className="barcode-restrict" src={cdnUrl('crown2.png', 'images')} alt="premium" />
          </Popconfirm>
        )}
        {!selectedJobWork && (
          <BarcodeReader
            productTypes={['STORABLE']}
            excludedProducts={data.map((item) => JSON.stringify(item?.product_sku_id))}
            excludeOutOfStock={selectedAdjustmentType === 'OUTWARD'}
            tenantDepartmentId={userTenantDepartmentId}
            filterReservedQuantity={selectedAdjustmentType === 'OUTWARD'}
            onSearch={(tenantSku) => {
              if (data[data?.length - 1]?.product_sku_id) {
                const copyData = data;
                data.push({
                  key: uuidv4(), asset: '', product_sku_name: '', availableQuantity: '', thresholdQuantity: '', quantity: '',
                });
                onSearchBarcodeReader(copyData, tenantSku, data[data?.length - 1]?.key);
              } else {
                handleProductChange(tenantSku, data[data?.length - 1]?.key);
              }
            }}
            disabled={!selectedAdjustmentType || !barCoding}
          />
        )}
      </div>
    </div>
  );
}

export default memo(AdjustmentFormFooter);