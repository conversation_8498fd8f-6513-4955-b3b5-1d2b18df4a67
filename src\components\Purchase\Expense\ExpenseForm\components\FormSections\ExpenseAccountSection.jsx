import React from 'react';
import { useExpenseForm } from '../../ExpenseFormContext';
import SelectExpenseAccount from '@Components/Common/SelectExpenseAccount';
import TallyLedgerSelector from '../../TallyLedgerSelector';
import TransactionTypeSelector from '../../TransactionTypeSelector';
import TaxableType from '@Components/Purchase/Common/TaxableType';
import NatureOfTransaction from '@Components/Purchase/Common/NatureOfTransaction';
import { entityNameEnum } from '@Apis/constants';
import { natureOfTransactionOptionCategory } from '@Components/Purchase/Common/NatureOfTransaction/natureOfTransactionConstants';
import { taxableTypeEnums } from '@Components/Purchase/Common/constants';

const ExpenseAccountSection = () => {
  const {
    expenseAccount,
    expenseTallyLedgerName,
    expenseTallyTransactionType,
    expenseTaxableType,
    expenseNatureOfTransaction,
    formSubmitted,
    createExpenseLoading,
    updateExpenseLoading,
    selectedTenant,
    itId,
    setFormState,
  } = useExpenseForm();

  if (!itId) return null;

  return (
    <div className="ant-col-md-24 ant-col-xs-24 fieldset-wrapper">
      <fieldset>
        <legend className="orgFormLabel" style={{ width: 'fit-content' }}>
          Expense Account
          <span style={{ color: 'red' }}>{'  *'}</span>
        </legend>

        <div className="ant-row">
          <div className="ant-col-md-12 ant-col-xs-24">
            <SelectExpenseAccount
              selectedTenant={selectedTenant}
              selectedAccount={expenseAccount?.account_id}
              onChange={(value) => {
                setFormState({
                  expenseAccount: value,
                });
              }}
              containerClass=""
              inputClassName="seller__selector"
              labelClassName="orgFormLabel"
              disabled={createExpenseLoading || updateExpenseLoading}
              showAddAccount
              hideTitle
              showError={formSubmitted && !expenseAccount}
              type="EXPENSE_ACCOUNT"
              title="Expense Account"
              filterOption={false}
            />
          </div>
        </div>

        <div className="ant-row">
          <div className="ant-col-md-12 ant-col-xs-24">
            <TallyLedgerSelector
              selectedTenant={selectedTenant}
              containerClassName={'orgInputContainer'}
              value={expenseTallyLedgerName}
              onChange={(value) => {
                if (value) {
                  setFormState({
                    expenseTallyLedgerName: value,
                    expenseTallyTransactionType: expenseTallyTransactionType || 'debit',
                  });
                }
              }}
              allowClear
              onClear={() => {
                setFormState({
                  expenseTallyLedgerName: '',
                });
              }}
              id={'expense_ledger_account_name'}
              title="Expense Account"
            />
          </div>

          <div className="ant-col-md-12 ant-col-xs-24">
            <TransactionTypeSelector
              containerClassName={'orgInputContainer'}
              id={'expense_transaction_type'}
              onChange={(value) => {
                setFormState({
                  expenseTallyTransactionType: value,
                  paidViaTallyTransactionType: value === 'DEBIT' ? 'CREDIT' : 'DEBIT',
                });
              }}
              value={expenseTallyTransactionType}
              disabled={createExpenseLoading || updateExpenseLoading}
              required
              title="Expense Account"
              showError={formSubmitted && !expenseTallyTransactionType}
            />
          </div>
        </div>

        <div className="ant-row">
          <div className="ant-col-md-12 ant-col-xs-24">
            <div className={'orgInputContainer'}>
              <label className="orgFormLabel">
                Expense Taxable Type
              </label>
              <TaxableType
                value={expenseTaxableType}
                onChange={(value) => {
                  setFormState({
                    expenseTaxableType: value,
                    expenseNatureOfTransaction: value === taxableTypeEnums.Non_GST ? 'System Inferred' : '',
                  });
                }}
                disabled={createExpenseLoading || updateExpenseLoading}
              />
            </div>
          </div>

          <div className="ant-col-md-12 ant-col-xs-24">
            <div className={'orgInputContainer'}>
              <label className="orgFormLabel">
                Expense Nature of Transaction
              </label>
              <NatureOfTransaction
                value={expenseNatureOfTransaction}
                onChange={(value) => {
                  setFormState({
                    expenseNatureOfTransaction: value,
                  });
                }}
                disabled={createExpenseLoading || updateExpenseLoading}
                taxableType={expenseTaxableType}
                entityName={entityNameEnum.EXPENSES}
                optionTypes={natureOfTransactionOptionCategory.ALL}
              />
            </div>
          </div>
        </div>
      </fieldset>
    </div>
  );
};

export default ExpenseAccountSection;
