import { PlusOutlined } from '@ant-design/icons';
import Constants from '@Apis/constants';
import DocumentNumberSeqInput from '@Components/Admin/Common/DocumentNumberSeqInput';
import CustomDocumentInputs from '@Components/Common/CustomDocumentInputs';
import CustomFieldV3 from '@Components/Common/CustomFieldV3';
import SelectDepartment from '@Components/Common/SelectDepartment';
import PRZSelect from '@Components/Common/UI/PRZSelect';
import PRZText from '@Components/Common/UI/PRZText';
import { DatePicker, Select, Upload } from 'antd';
import React, { Fragment, memo } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { actionTypes } from '../reducer';

const { Option } = Select;
const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </div>
);

function AdjustmentDocFields({
  localDispatch,
  selectedJobWork,
  state,
  user,
  selectedPRForAdjustment,
  createIndentLoading,
  adjustmentTypes,
  customMessageStockAdjustment,
  getProductsSuccess,
  getInventoryLocations,
  getProductBySkusLoading,
  resetData,
  customInputChange,
}) {

  const { selectedAdjustmentType, selectedAdjustmentReason, userDepartmentId, selectedDate, cfStockAdjustmentDoc, formSubmitted, adjustmentBasedOn, selectedTenant, adjustmentNumber, docSeqId, description, fileList } = state;

  const inwardAdjustmentReasons = [];
  const outwardAdjustmentReasons = [];

  // eslint-disable-next-line unicorn/no-array-for-each
  customMessageStockAdjustment?.data?.forEach((reasons) => {
    if (reasons.type === 'OUTWARD') {
      outwardAdjustmentReasons.push(reasons.message);
    } else if (reasons.type === 'INWARDS') {
      inwardAdjustmentReasons.push(reasons.message);
    }
  });

  return (
    <Fragment>
      {!selectedJobWork && (
        <div className="ant-row">
          <div className="ant-col-md-24">
            <div className="form__section">
              <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                <PRZText text="PART A" className="form__section-title" />
                <div className="form__section-line" />
                <div style={{
                  display: 'flex', justifyContent: 'end', alignItems: 'center', marginLeft: '10px',
                }}
                >
                  <CustomDocumentInputs
                    customFields={cfStockAdjustmentDoc}
                    updateCustomFields={(cf) => localDispatch({
                      type: actionTypes.SET_FIELD,
                      field: 'cfStockAdjustmentDoc',
                      value: cf,
                    })}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      <div className="form__section">
        <div className="form__section-inputs mg-bottom-20">
          <div className="ant-row">
            <DocumentNumberSeqInput
              valueFromProps={adjustmentNumber}
              setInitialDocSeqNumber={(value) => localDispatch({
                type: actionTypes.SET_FIELD,
                field: 'initialAdjustmentNumber',
                value: value,
              })}
              entityName="INVENTORY_ADJUSTMENT"
              docSeqId={docSeqId}
              tenantId={selectedTenant?.tenant_id || user?.tenant_info?.tenant_id}
              onChangeFromProps={(event, newValue, seqId) => {
                localDispatch({
                  type: actionTypes.SET_MULTIPLE_FIELDS,
                  payload: {
                    adjustmentNumber: newValue ? (newValue || '') : (event?.target?.value || ''),
                    docSeqId: seqId,
                  },
                });
              }}
              docTitle="Adjustment Number#"
              formSubmitted={formSubmitted}
            />
            {!selectedJobWork && (
              <div className="ant-col-md-6">
                <div className="form__input-row">
                  <PRZText className="form__input-row__label" text="Adjustment Type" required />
                  {selectedPRForAdjustment
                    ? (
                      <PRZText className="form__input-row__text" text="Outward" />
                    )
                    : (
                      <div className={`form__input-row__input ${(formSubmitted && !selectedAdjustmentType) ? 'form__input-row__input-error' : ''}`}>
                        <PRZSelect
                          getPopupContainer={(triggerNode) => triggerNode.parentNode}
                          value={selectedAdjustmentType}
                          onChange={(value) => {
                            localDispatch({
                              type: actionTypes.SET_MULTIPLE_FIELDS,
                              payload: {
                                selectedAdjustmentType: value,
                                selectedAdjustmentReason: '',
                                data: [{
                                  key: uuidv4(), asset: '', product_name: '', availableQuantity: '', thresholdQuantity: '', quantity: null,
                                }],
                                adjustmentBasedOn: value === 'RECONCILIATION' ? 'AVAILABLE_STOCK' : 'CURRENT_STOCK',
                              },
                            });
                          }}
                          disabled={createIndentLoading}
                          status={(formSubmitted && !selectedAdjustmentType) ? 'error' : ''}
                        >
                          {adjustmentTypes?.map((adjustment) => (
                            <Option key={adjustment} value={adjustment}>
                              {adjustment.replaceAll('_', ' ')?.toProperCase()}
                            </Option>
                          ))}
                        </PRZSelect>
                      </div>
                    )}
                </div>
              </div>
            )}
            {selectedAdjustmentType !== 'RECONCILIATION' && (
              <div className="ant-col-md-6">
                <div className="form__input-row">
                  <PRZText text="Adjustment Reason#" className="form__input-row__label" required />
                  {selectedPRForAdjustment
                    ? <PRZText className="form__input-row__text" text="Inventory Revaluation" required />
                    : (
                      <div className={`form__input-row__input ${(formSubmitted && !selectedAdjustmentReason) ? 'form__input-row__input-error' : ''}`}>
                        <PRZSelect
                          getPopupContainer={(triggerNode) => triggerNode.parentNode}
                          value={selectedAdjustmentReason}
                          onChange={(value) => {
                            localDispatch({
                              type: actionTypes.SET_FIELD,
                              field: 'selectedAdjustmentReason',
                              value: value,
                            });
                          }}
                          disabled={createIndentLoading}
                          status={(formSubmitted && !selectedAdjustmentReason) ? 'error' : ''}
                        >
                          {selectedAdjustmentType === 'INWARDS' && inwardAdjustmentReasons?.map((adjustment) => (
                            <Option key={adjustment} value={adjustment}>
                              {adjustment.replaceAll('_', ' ')?.toProperCase()}
                            </Option>
                          ))}
                          {selectedAdjustmentType === 'OUTWARD' && outwardAdjustmentReasons?.map((adjustment) => (
                            <Option key={adjustment} value={adjustment}>
                              {adjustment.replaceAll('_', ' ')?.toProperCase()}
                            </Option>
                          ))}
                        </PRZSelect>
                      </div>
                    )}
                </div>
              </div>
            )}
            <div className="ant-col-md-6">
              <div className="form__input-row">
                <PRZText text="Select Department" className="form__input-row__label" required />
                <div className="form__input-row__input">
                  <SelectDepartment
                    selectedDepartment={userDepartmentId}
                    showSearch
                    onChange={(value) => {
                      getProductsSuccess(null);
                      getInventoryLocations(value?.tenant_department_id, '', null, '');
                      localDispatch({
                        type: actionTypes.SET_MULTIPLE_FIELDS,
                        payload: {
                          department: value,
                          userDepartmentId: value?.department_id,
                          userTenantDepartmentId: value?.tenant_department_id,
                        },
                      });
                      resetData();
                    }}
                    tenantId={selectedTenant?.tenant_id || user?.tenant_info?.tenant_id}
                    containerClassName=""
                    disabled={createIndentLoading || selectedJobWork}
                    hideTitle
                    emptyNotAllowed
                  />
                </div>
              </div>
            </div>
            {!selectedJobWork && (
              <div className="ant-col-md-6">
                <div className="form__input-row">
                  <PRZText text="Adjustment Date" className="form__input-row__label" required />
                  <div className={`form__input-row__input ${(formSubmitted && !selectedDate) ? 'form__input-row__input-error' : ''}`}>
                    <DatePicker
                      value={selectedDate}
                      onChange={(value) => {
                        localDispatch({
                          type: actionTypes.SET_FIELD,
                          field: 'selectedDate',
                          value: value,
                        });
                      }}
                      format="DD-MMM-YYYY"
                      disabled={createIndentLoading}
                      style={{
                        borderColor: formSubmitted && !selectedDate ? 'red' : 'rgba(68, 130, 218, 0.2)',
                        border: '1px solid rgba(68, 130, 218, 0.2)',
                        borderRadius: '2px',
                        height: '28px',
                        padding: '1px 3px',
                        width: '100%',
                        background: 'white',
                      }}
                      status={(formSubmitted && !selectedDate) ? 'error' : ''}
                    />
                  </div>
                </div>
              </div>
            )}
            <div className="ant-col-md-6">
              <div className="form__input-row">
                <PRZText text="Stock Based On" className="form__input-row__label" />
                <div className="form__input-row__input">
                  <PRZSelect
                    filterOption={false}
                    showSearch
                    disabled={createIndentLoading || getProductBySkusLoading || selectedAdjustmentType === 'RECONCILIATION'}
                    value={adjustmentBasedOn}
                    onChange={(value) => {
                      localDispatch({
                        type: actionTypes.SET_FIELD,
                        field: 'adjustmentBasedOn',
                        value: value,
                      });
                    }}
                  >
                    <Option value="AVAILABLE_STOCK" key="AVAILABLE_STOCK"> Based on Current Available Stock</Option>
                    <Option value="CURRENT_STOCK" key="CURRENT_STOCK"> Based on Current Consumed Stock</Option>
                  </PRZSelect>
                </div>
              </div>
            </div>
            <CustomFieldV3
              customFields={cfStockAdjustmentDoc}
              formSubmitted={formSubmitted}
              customInputChange={(value, cfId) => customInputChange(value, cfId)}
              wrapperClassName="ant-col-md-6"
              containerClassName="form__input-row"
              labelClassName="form__input-row__label"
              inputClassName="form__input-row__input"
              errorClassName="form__input-row__input-error"
              disableCase={createIndentLoading}
              hideTitle
            />
          </div>
          <div className="ant-row">
            <div className="ant-col-md-6">
              <div className="form__input-row">
                <PRZText text="Internal Remarks" className="form__input-row__label" />
                <div className="form__input-row__input">
                  <textarea
                    rows="5"
                    cols="50"
                    value={description}
                    onChange={(e) => localDispatch({
                      type: actionTypes.SET_FIELD,
                      field: 'description',
                      value: e.target.value,
                    })}
                    disabled={createIndentLoading}
                    style={{
                      resize: 'none',
                      backgroundColor: 'rgb(239, 239, 239)',
                      border: 'none',
                      borderRadius: '4px',
                      marginRight: '15px',
                      height: '103px',
                      padding: '4px 8px',
                      width: '100%',
                    }}
                  />
                </div>
              </div>
            </div>
            <div className="ant-col-md-6">
              <div className="form__input-row">
                <PRZText className="form__input-row__label" text="Attachment" />
                <Upload
                  action={Constants.UPLOAD_FILE}
                  listType="picture-card"
                  fileList={fileList}
                  disabled={createIndentLoading}
                  onChange={(fileListData) => {
                    localDispatch({
                      type: actionTypes.SET_FIELD,
                      field: 'fileList',
                      value: fileListData?.fileList?.map((item) => ({
                        ...item,
                        url: item?.response?.response?.location || item?.url,
                      })),
                    });
                  }}
                  multiple
                >
                  {fileList?.length >= 20 ? null : uploadButton}
                </Upload>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
}

export default memo(AdjustmentDocFields);