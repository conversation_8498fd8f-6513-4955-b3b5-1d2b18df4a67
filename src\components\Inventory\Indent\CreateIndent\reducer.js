/* eslint-disable unicorn/prefer-structured-clone */
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { createData, getBatches, getBatchMethod } from './helper';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import { INFINITE_EXPIRY_DATE, toISTDate } from '@Apis/constants';
import Helpers from '@Apis/helpers';
import Decimal from 'decimal.js';

export const initialState = {
  toggleRejectedBatches: false,
  isBulkUpload: false,
  isBatchValid: false,
  data: [
    {
      key: uuidv4(), asset: '', product_sku_name: '', availableQuantity: '', thresholdQuantity: '', quantity: null,
    },
  ],
  adjustmentTypes: [
    'INWARDS',
    'OUTWARD',
    'RECONCILIATION',
  ],
  adjustmentBasedOn: 'CURRENT_STOCK',
  allStock: true,
  selectedDate: dayjs(),
  selectedAdjustmentType: 'INWARDS',
};

export const actionTypes = {
  SET_FIELD: 'SET_FIELD',
  SET_MULTIPLE_FIELDS: 'SET_MULTIPLE_FIELDS',
  HANDLE_PRODUCT_CHANGE_VALUE: 'HANDLE_PRODUCT_CHANGE_VALUE',
  HANDLE_PRODUCT_CHANGE: 'HANDLE_PRODUCT_CHANGE',
  ONSEARCH_BARCODE_READER: 'ONSEARCH_BARCODE_READER',
  HANDLE_DELETE: 'HANDLE_DELETE',
  ADD_NEW_ROW: 'ADD_NEW_ROW',
  CUSTOM_INPUT_CHANGE: 'CUSTOM_INPUT_CHANGE',
  RESET_DATA: 'RESET_DATA',
  TOGGLE_BATCH: 'TOGGLE_BATCH',
  UPDATE_BATCH_CFS: 'UPDATE_BATCH_CFS',
  ADD_NEW_BATCH: 'ADD_NEW_BATCH',
  REMOVE_NEW_BATCH: 'REMOVE_NEW_BATCH',
  SET_SELECTED_BATCH: 'SET_SELECTED_BATCH',
};

export const reducer = (state, action) => {
  switch (action.type) {
  case 'SET_FIELD': {
    return {
      ...state,
      [action.field]: action.value,
    };
  }

  case 'SET_MULTIPLE_FIELDS': {
    return {
      ...state,
      ...action.payload,
    };
  }

  case 'HANDLE_PRODUCT_CHANGE_VALUE': {
    const { value, key } = action.payload;

    const updatedData = state?.data?.map((item) =>
      item?.key === key
        ? { ...item, product_sku_name: value }
        : item
    );

    return {
      ...state,
      data: updatedData,
    };
  }

  case 'HANDLE_PRODUCT_CHANGE': {
    const { tenantSku, key, otherDepStocksData, isMultiMode, callback, user, inventoryLocations, cfV2DocStockAdjustment,
    } = action.payload;

    const { data, userTenantDepartmentId, selectedAdjustmentType } = state;

    const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;
    const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocStockAdjustment?.data?.batch_custom_fields, true);

    if (isMultiMode && Array.isArray(tenantSku)) {
      const copyData = [];

      for (const tenantSkuData of tenantSku) {
        const currentOtherDepStocksData = otherDepStocksData?.find((item) => item?.product_sku_id === tenantSkuData?.product_sku_id);

        const newData = {
          key: uuidv4(),
        };

        const copyDataItem = createData({
          dataItem: newData,
          tenantSku: tenantSkuData,
          otherDepStocksData: currentOtherDepStocksData,
          userTenantDepartmentId,
          inventoryLocations,
          selectedAdjustmentType,
          autoPrintDescription,
          batchCustomFields,
        });

        copyData.push(copyDataItem);
      }

      if (callback) {
        callback();
      }

      return {
        ...state,
        data: [...state.data, ...copyData],
      };
    } else {
      data?.map((item) => {
        if (item.key === key) {
          const copyDataItem = createData({
            dataItem: item,
            tenantSku,
            otherDepStocksData,
            userTenantDepartmentId,
            inventoryLocations,
            selectedAdjustmentType,
            autoPrintDescription,
            batchCustomFields,
          });

          return copyDataItem;
        }
        return item;
      });

      return {
        ...state,
        data,
      };
    }
  }

  case 'ONSEARCH_BARCODE_READER': {
    const { tenantSku, key, otherDepStocksData, isMultiMode, callback, user, inventoryLocations, cfV2DocStockAdjustment, data
    } = action.payload;

    const { userTenantDepartmentId, selectedAdjustmentType } = state;

    const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;
    const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocStockAdjustment?.data?.batch_custom_fields, true);

    if (isMultiMode && Array.isArray(tenantSku)) {
      const copyData = [];

      for (const tenantSkuData of tenantSku) {
        const currentOtherDepStocksData = otherDepStocksData?.find((item) => item?.product_sku_id === tenantSkuData?.product_sku_id);

        const newData = {
          key: uuidv4(),
        };

        const copyDataItem = createData({
          dataItem: newData,
          tenantSku: tenantSkuData,
          otherDepStocksData: currentOtherDepStocksData,
          userTenantDepartmentId,
          inventoryLocations,
          selectedAdjustmentType,
          autoPrintDescription,
          batchCustomFields,
        });

        copyData.push(copyDataItem);
      }

      if (callback) {
        callback();
      }

      return {
        ...state,
        data: [...state.data, ...copyData],
      };
    } else {
      data?.map((item) => {
        if (item.key === key) {
          const copyDataItem = createData({
            dataItem: item,
            tenantSku,
            otherDepStocksData,
            userTenantDepartmentId,
            inventoryLocations,
            selectedAdjustmentType,
            autoPrintDescription,
            batchCustomFields,
          });

          return copyDataItem;
        }
        return item;
      });

      return {
        ...state,
        data,
      };
    }
  }

  case 'HANDLE_DELETE': {
    const { key } = action.payload;
    const { data } = state;
    if (data.length === 1) {
      return {
        ...state,
        data: [{
          key: uuidv4(), asset: '', product_sku_name: '', availableQuantity: '', thresholdQuantity: '', quantity: null,
        }],
      };
    } else {
      const copyData = data.filter((item) => item.key !== key);
      return {
        ...state,
        data: copyData,
      };
    }
  }

  case 'ADD_NEW_ROW': {
    const { data } = state;
    const { callback } = action.payload;
    const copyData = JSON.parse(JSON.stringify(data));
    const key = uuidv4();
    copyData.push({
      key, asset: '', product_sku_name: '', availableQuantity: '', thresholdQuantity: '', quantity: '',
    });
    if (callback) {
      callback(key);
    }
    return {
      ...state,
      data: copyData,
    };
  }

  case 'CUSTOM_INPUT_CHANGE': {
    const { fieldValue, cfId } = action.payload;
    const { cfStockAdjustmentDoc } = state;
    const newCustomField = cfStockAdjustmentDoc.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })),
          };
        }
        return {
          ...customField,
          fieldValue,
        };
      }
      return {
        ...customField,
      };
    });
    return {
      ...state,
      cfStockAdjustmentDoc: newCustomField,
    };
  }

  case 'RESET_DATA': {
    return {
      ...state,
      data: [
        {
          key: uuidv4(), asset: '', product_sku_name: '', availableQuantity: '', thresholdQuantity: '', quantity: null,
        },
      ],
    };
  }

  case 'TOGGLE_BATCH': {
    const { record, adjustmentRow } = action.payload;

    const copyData = state.data.map((item) => {
      if (item?.key !== adjustmentRow?.key) return item;

      let updatedQuantity = item.quantity;

      const updatedBatches = item.available_batches.map((batch) => {
        if (batch?.batch_id !== record?.batch_id) return batch;

        let newConsumed = batch.consumed_qty;
        let inUse = batch.batch_in_use;

        if (inUse) {
          updatedQuantity -= batch.consumed_qty;
          newConsumed = 0;
        } else {
          newConsumed = batch.available_qty;
          updatedQuantity += batch.available_qty;
        }

        return {
          ...batch,
          consumed_qty: newConsumed,
          batch_in_use: !inUse,
        };
      });

      return {
        ...item,
        quantity: updatedQuantity,
        available_batches: updatedBatches,
      };
    });

    return { ...state, data: copyData };
  }

  case 'UPDATE_BATCH_CFS': {
    const { fieldValue, cfId, record } = action.payload;
    const { data } = state;
    const copyData = data.map((item) => {
      if (item?.key === record?.key) {
        const copylineCfs = item?.selectedBatch?.custom_fields;
        for (const cf of copylineCfs) {
          if (cf.cfId === cfId) {
            cf.fieldValue = cf.fieldType === 'ATTACHMENT' ? fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })) : fieldValue;
            break;
          }
        }
        return { ...item, selectedBatch: { ...item.selectedBatch, custom_fields: copylineCfs } };
      }
      return item;
    });
    return {
      ...state,
      data: copyData,
    };
  }

  case 'ADD_NEW_BATCH': {
    const { batch, callback } = action.payload;
    const { data, selectedLineInfo } = state;
    let selectedItem;
    const copyData = data.map((item) => {
      if (item?.key === selectedLineInfo?.key) {
        selectedItem = item;
        return { ...item, available_batches: [batch, ...(item?.available_batches || [])], selectedBatch: batch };
      }
      return item;
    });
    if (callback) callback();
    return selectedItem ? {
      ...state,
      data: copyData,
      availableBatches: selectedItem?.available_batches,
      selectedLineInfo: {
        ...selectedLineInfo,
        expiryDays: selectedItem?.expiryDays,
        batchUom: selectedItem?.uom_info,
        selectedBatch: batch,
        uomList: selectedItem?.uom_list,
        tenantProductId: selectedItem?.tenant_product_id,
        key: selectedItem?.key,
      },
    } : {
      ...state,
      data: copyData,
    };
  }

  case 'REMOVE_NEW_BATCH': {
    const { data, selectedLineInfo } = state;
    let selectedItem;
    const dataCopy = JSON.parse(JSON.stringify(data));
    for (const element of dataCopy) {
      if (element?.key === selectedLineInfo?.key) {
        selectedItem = element;
        element.selectedBatch = (element.available_batches[1] || {});
        element.available_batches = (element.available_batches || []).filter((item) => item?.batch_id);
      }
    }
    return selectedItem ? {
      ...state,
      data: dataCopy,
      availableBatches: selectedItem?.available_batches,
      selectedLineInfo: {
        ...selectedLineInfo,
        expiryDays: selectedItem?.expiryDays,
        batchUom: selectedItem?.uom_info,
        selectedBatch: selectedItem?.available_batches[0] || {},
        uomList: selectedItem?.uom_list,
        tenantProductId: selectedItem?.tenant_product_id,
        key: selectedItem?.key,
      },
    } : {
      ...state,
      data: dataCopy,
    };
  }

  case 'SET_SELECTED_BATCH': {
    const { batch } = action.payload;
    const { data, selectedLineInfo } = state;
    let selectedItem;
    const copyData = data.map((item) => {
      if (item?.key === selectedLineInfo?.key) {
        selectedItem = item;
        return { ...item, selectedBatch: batch };
      }
      return item;
    });
    return selectedItem ? {
      ...state,
      data: copyData,
      availableBatches: selectedItem?.available_batches,
      selectedLineInfo: {
        ...selectedLineInfo,
        expiryDays: selectedItem?.expiryDays,
        batchUom: selectedItem?.uom_info,
        selectedBatch: batch,
        uomList: selectedItem?.uom_list,
        tenantProductId: selectedItem?.tenant_product_id,
        key: selectedItem?.key,
      },
    } : {
      ...state,
      data: copyData,
    };
  }

  case 'UPDATE_BATCH_VALUE': {
    const { key, value, label, batchId } = action.payload;
    const { data } = state;
    const copyData = data?.map((item) => {
      if (item?.key === key) {
        const copyBatches = item?.available_batches?.map((batch) => {
          if (batch?.batch_id === batchId) {
            if (label === 'consumed_qty') {
              return {
                ...batch,
                [label]: Math.min(value, batch?.available_qty),
              };
            }
            return {
              ...batch,
              [label]: value,
            };
          }
          return batch;
        });
        let qty = new Decimal(0);
        for (const batch of copyBatches) {
          qty = qty.plus(new Decimal(batch?.consumed_qty || 0));
        }
        qty = qty.toNumber();
        return {
          ...item,
          available_batches: copyBatches,
          quantity: qty,
        };
      }
      return item;
    });
    return {
      ...state,
      data: copyData,
    };
  }

  default: {
    return state;
  }
  };
};
