import React, { Fragment } from 'react';
import { useExpenseForm } from '../../ExpenseFormContext';
import SelectExpenseAccount from '@Components/Common/SelectExpenseAccount';
import TallyLedgerSelector from '../../TallyLedgerSelector';
import TransactionTypeSelector from '../../TransactionTypeSelector';
import TaxableType from '@Components/Purchase/Common/TaxableType';
import NatureOfTransaction from '@Components/Purchase/Common/NatureOfTransaction';
import { entityNameEnum } from '@Apis/constants';
import { natureOfTransactionOptionCategory } from '@Components/Purchase/Common/NatureOfTransaction/natureOfTransactionConstants';
import { taxableTypeEnums } from '@Components/Purchase/Common/constants';

const PaidViaSection = () => {
  const {
    assetAccount,
    paidViaTallyLedgerName,
    paidViaTallyTransactionType,
    paidViaTaxableType,
    paidViaNatureOfTransaction,
    formSubmitted,
    createExpenseLoading,
    updateExpenseLoading,
    selectedTenant,
    itId,
    setFormState,
    expenseAccount,
  } = useExpenseForm();

  if (!itId) {
    return (
      <Fragment>
        <div className="ant-col-md-12 ant-col-xs-24">
          <SelectExpenseAccount
            selectedAccount={expenseAccount?.account_id}
            onChange={(value) => {
              setFormState({
                expenseAccount: value,
              });
            }}
            containerClass="orgInputContainer"
            inputClassName="seller__selector"
            labelClassName="orgFormLabel"
            disabled={createExpenseLoading || updateExpenseLoading}
            showAddAccount
            title="Expense Account"
            type="EXPENSE_ACCOUNT"
            filterOption={false}
            showError={formSubmitted && !expenseAccount}
          />
        </div>
        <div className="ant-col-md-12 ant-col-xs-24">
          <SelectExpenseAccount
            selectedAccount={assetAccount?.account_id}
            onChange={(value) => {
              setFormState({
                assetAccount: value,
              });
            }}
            containerClass="orgInputContainer"
            inputClassName="seller__selector"
            labelClassName="orgFormLabel"
            disabled={createExpenseLoading || updateExpenseLoading}
            showAddAccount
            title="Paid Via"
            type="ASSET_ACCOUNT"
            filterOption={false}
            showError={formSubmitted && !assetAccount}
          />
        </div>
      </Fragment>
    );
  }

  return (
    <div className="ant-col-md-24 ant-col-xs-24 fieldset-wrapper">
      <fieldset>
        <legend className="orgFormLabel" style={{ width: 'fit-content' }}>
          Paid Via
          <span style={{ color: 'red' }}>{'  *'}</span>
        </legend>

        <div className="ant-row">
          <div className="ant-col-md-12 ant-col-xs-24">
            <SelectExpenseAccount
              selectedAccount={assetAccount?.account_id}
              onChange={(value) => {
                setFormState({
                  assetAccount: value,
                });
              }}
              containerClass=""
              inputClassName="seller__selector"
              labelClassName="orgFormLabel"
              disabled={createExpenseLoading || updateExpenseLoading}
              showAddAccount
              hideTitle
              showError={formSubmitted && !assetAccount}
              type="ASSET_ACCOUNT"
              title="Paid Via"
              filterOption={false}
            />
          </div>
        </div>

        <div className="ant-row">
          <div className="ant-col-md-12 ant-col-xs-24">
            <TallyLedgerSelector
              selectedTenant={selectedTenant}
              containerClassName={'orgInputContainer'}
              value={paidViaTallyLedgerName}
              onChange={(value) => {
                if (value) {
                  setFormState({
                    paidViaTallyLedgerName: value,
                    paidViaTallyTransactionType: paidViaTallyTransactionType || 'credit',
                  });
                }
              }}
              allowClear
              onClear={() => {
                setFormState({
                  paidViaTallyLedgerName: '',
                });
              }}
              id={'paid_via_ledger_account_name'}
              title="Paid Via"
            />
          </div>

          <div className="ant-col-md-12 ant-col-xs-24">
            <TransactionTypeSelector
              containerClassName={'orgInputContainer'}
              id={'paid_via_transaction_type'}
              onChange={(value) => {
                setFormState({
                  paidViaTallyTransactionType: value,
                  expenseTallyTransactionType: value === 'DEBIT' ? 'CREDIT' : 'DEBIT',
                });
              }}
              value={paidViaTallyTransactionType}
              disabled={createExpenseLoading || updateExpenseLoading}
              required
              title="Paid Via"
              showError={formSubmitted && !paidViaTallyTransactionType}
            />
          </div>
        </div>

        <div className="ant-row">
          <div className="ant-col-md-12 ant-col-xs-24">
            <div className={'orgInputContainer'}>
              <label className="orgFormLabel">
                Paid Via Taxable Type
              </label>
              <TaxableType
                value={paidViaTaxableType}
                onChange={(value) => {
                  setFormState({
                    paidViaTaxableType: value,
                    paidViaNatureOfTransaction: value === taxableTypeEnums.Non_GST ? 'System Inferred' : '',
                  });
                }}
                disabled={createExpenseLoading || updateExpenseLoading}
              />
            </div>
          </div>

          <div className="ant-col-md-12 ant-col-xs-24">
            <div className={'orgInputContainer'}>
              <label className="orgFormLabel">
                Paid Via Nature of Transaction
              </label>
              <NatureOfTransaction
                value={paidViaNatureOfTransaction}
                onChange={(value) => {
                  setFormState({
                    paidViaNatureOfTransaction: value,
                  });
                }}
                disabled={createExpenseLoading || updateExpenseLoading}
                taxableType={paidViaTaxableType}
                entityName={entityNameEnum.EXPENSES}
                optionTypes={natureOfTransactionOptionCategory.ALL}
              />
            </div>
          </div>
        </div>
      </fieldset>
    </div>
  );
};

export default PaidViaSection;
