import { PlusCircleFilled } from '@ant-design/icons';
import BulkUpload from '@Components/Common/BulkUpload';
import PRZText from '@Components/Common/UI/PRZText';
import React, { memo } from 'react';
import { actionTypes } from '../reducer';
import { Table } from 'antd';
import BatchSelector from '../BatchSelector';
import Helpers from '@Apis/helpers';
import { getColumns } from '../helper';

function AdjustmentDocLines({
  localDispatch,
  data,
  selectedJobWork,
  createIndentLoading,
  userTenantDepartmentId,
  selectedAdjustmentType,
  createNewBatch,
  addNewRow,
  getProductBySkusLoading,
  allStock,
  toggleBatch,
  updateBatchValue,
  onBulkUpload,
  adjustmentBasedOn,
  formSubmitted,
  selectedTenant,
  user,
  handleProductChange,
  handleProductChangeValue,
  handleMultiProductChange,
  isBatchValid,
  handleDelete,
  updateBatchCfs,
  isSecondaryUomEnabled,
}) {
  return (
    <div>
      <div className="create-indent__header">
        <div className="flex-align-c" style={{ marginBottom: '10px', marginTop: '10px' }}>
          <PRZText text={`Adjustment Products (${data.filter((item) => item.product_sku_name?.length > 0)?.length})`} className="create-indent__table-label" />
          {!selectedJobWork && (
            <div className="create-indent__bulk-upload">
              <BulkUpload
                userDepartmentId={userTenantDepartmentId}
                disabled={!selectedAdjustmentType || !userTenantDepartmentId}
                createNewBatch={createNewBatch}
                disableBatchCreate={!!(selectedAdjustmentType === 'OUTWARD')}
                setCreateNewBatch={() => localDispatch({
                  type: actionTypes.SET_MULTIPLE_FIELDS,
                  payload: { createNewBatch: !createNewBatch },
                })}
                onBulkUpload={(updatedData, importedData) => onBulkUpload(updatedData, importedData)}
                adjustmentType={selectedAdjustmentType}
                customClass="custom-doc-columns__wrapper"
              />
            </div>
          )}
        </div>
        <div className="generic-table-container">
          <Table
            bordered
            showHeader
            size="small"
            loading={createIndentLoading || getProductBySkusLoading}
            columns={getColumns({
              localDispatch,
              selectedAdjustmentType,
              adjustmentBasedOn,
              formSubmitted,
              data,
              userTenantDepartmentId,
              selectedTenant,
              user,
              selectedJobWork,
              handleProductChange,
              handleProductChangeValue,
              handleMultiProductChange,
              allStock,
              isBatchValid,
              handleDelete,
              updateBatchCfs,
              isSecondaryUomEnabled,
            })}
            dataSource={data}
            currentPage
            scroll={{ x: 'max-content' }}
            pagination={onBulkUpload ? false : { pageSize: 25 }}
            expandable={{
              // defaultExpandAllRows: true,
              rowExpandable: (record) => {
                if (!record.product_sku_name || !record.available_batches?.length) {
                  return false;
                }
                switch (selectedAdjustmentType) {
                case 'INWARDS': {
                  return false;
                }
                case 'OUTWARD': {
                  return record.availableQuantity > 0;
                }
                case 'RECONCILIATION': {
                  return Number(record.newQuantity) <= Number(record.availableQuantity ?? 0);
                }

                default: {
                  return true;
                }
                }
              },
              expandedRowRender: (record) => (
                <BatchSelector
                  allStock={allStock}
                  upDateAllStock={() => {
                    const filteredData = data?.map((item) => {
                      if (record?.key === item?.key) {
                        const updatedBatches = item.available_batches?.map((batch) => ({
                          ...batch,
                          consumed_qty: 0,
                        }));
                        return {
                          ...item,
                          quantity: 0,
                          newQuantity: 0,
                          available_batches: item?.oldBatches?.length > 0 ? item?.oldBatches?.map((batch) => ({
                            ...batch,
                            consumed_qty: 0,
                          })) : updatedBatches,
                        };
                      }
                      return item;
                    });
                    localDispatch({
                      type: actionTypes.SET_MULTIPLE_FIELDS,
                      payload: { data: filteredData, allStock: !allStock },
                    });
                  }}
                  batchMethod={record?.batchConsumptionMethod}
                  updateBatchConsumptionMethod={(value) => {
                    const sortedData = data?.map((item) => {
                      if (record.key === item.key) {
                        return {
                          ...item,
                          batchConsumptionMethod: value,
                          available_batches: Helpers.batchSelectionMethod(value, item.available_batches, item.quantity, allStock),
                        };
                      }
                      return item;
                    });
                    localDispatch({
                      type: actionTypes.SET_MULTIPLE_FIELDS,
                      payload: { data: sortedData },
                    });
                  }}
                  batches={record?.available_batches}
                  uomInfo={record?.uom_info}
                  productUomInfo={record?.uom_info}
                  expiryDays={record?.expiry_days}
                  itemsRow={record}
                  onToggleBatch={(batch, adjustmentRow) => toggleBatch(batch, adjustmentRow)}
                  updateBatchValue={(value, label, batchId) => updateBatchValue(record?.key, value, label, batchId)}
                  isBulkUploaded={record?.isBulkUploaded}
                />
              ),
            }}
          />
        </div>
      </div>
      <div className="create-indent__details">
        <div className="new-row-button" onClick={() => addNewRow()}>
          <span className="new-row-button__icon"><PlusCircleFilled /></span>
          <div>New Item</div>
        </div>
      </div>
    </div>
  );
}

export default memo(AdjustmentDocLines);