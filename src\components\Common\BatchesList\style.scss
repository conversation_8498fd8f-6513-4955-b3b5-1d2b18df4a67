.batch-list__title {
   margin-bottom: 15px;
}

.batch-list__item {

   position: relative;
   line-height: 20px;
   border: 1px solid rgba(45, 124, 247, 0.2);
   border-radius: 5px;
   padding: 10px;
   margin-bottom: 15px;
   cursor: pointer;

   &:hover {
      background-color: rgba(210, 210, 210, 0.1);
   }

   .batch-list__item-title {
      color: #2d7cf7;
      font-weight: 500;
      font-size: 12px;
   }

   .batch-list__item-info {
      font-size: 11px;
      margin-top: 5px;
      font-weight: 500;
      line-height: 18px;
   }

   .batch-shelf-life {
      display: flex;
      align-items: center;
      background-color: white;
      border-radius: 5px;
      padding: 5px;
      margin-top: 5px;
      font-size: 10px;
      font-weight: 600;

      .progress-line__wrapper {
         height: 8px;
         border-radius: 10px;
         margin-right: 7px;

         .progress-line {
            height: 8px;
            border-radius: 10px;
         }
      }

      .ant-progress {
         width: 120px !important;
      }
   }

   .batch-list__item-selected-tag {
      position: absolute;
      right: 0px;
      top: 5px;
      background-color: rgb(23, 181, 77);
      color: white;
      border-radius: 20px 0px 0px 20px;
      padding: 1px 5px 0px 10px;
      font-size: 12px;
      font-weight: 600;
   }
}

.batch-list__item-selected {
   background-color: rgba(45, 247, 62, 0.1);

   &:hover {
      background-color: rgba(45, 247, 62, 0.1);
   }
}

.new-batch__button {
   background-color: #2d7cf7 !important;
   position: absolute;
   z-index: 1000;
   right: 20px;
   bottom: 20px;
   padding: 10px;
   border-radius: 30px;
   font-size: 16px;
   color: white;
   cursor: pointer;
   box-shadow: 0px 6px 20px rgba(173, 173, 173, .35) !important;
   transition: 0.2s;

   &:hover {
      box-shadow: 0px 6px 20px rgba(173, 173, 173, 1) !important;
   }
}

.new-batch__tag-warning {
   font-size: 12px;
   margin-top: 5px;
}

.batch-list__item-remove-wrapper {
   display: flex;

   .batch-list__item-remove {
      color: #2d7cf7;
      cursor: pointer;
      margin-top: -10px;
      margin-bottom: 15px;
      display: inline-block;
      font-size: 12px;
      margin-left: auto;
      text-decoration: underline;
   }
}

.ia-type-tag {
   display: flex;
   justify-content: center;
   align-items: center;
   color: white;
   font-weight: 600;
   border-radius: 3px;
   padding: 2px 5px 2px 2px;
   text-align: center;
   width: 70px;
   font-size: 10.5px;
   margin-top: 10px;
   z-index: 1;
}