import React, { Fragment, useCallback, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { notification } from 'antd';
import dayjs from 'dayjs';

import ExpenseActions from '@Actions/purchase/expense/expenseActions';
import UserActions from '@Actions/userActions';
import CfV2Actions from '@Actions/configurations/cfV2Actions';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import { ISTDateFormat } from '@Apis/constants';

import { ExpenseFormProvider, useExpenseForm } from './ExpenseFormContext';
import BasicInfoSection from './components/FormSections/BasicInfoSection';
import ExpenseAccountSection from './components/FormSections/ExpenseAccountSection';
import PaidViaSection from './components/FormSections/PaidViaSection';
import AmountAndTaxSection from './components/FormSections/AmountAndTaxSection';
import SellerAndCustomerSection from './components/FormSections/SellerAndCustomerSection';
import ReferenceAndAdjustmentSection from './components/FormSections/ReferenceAndAdjustmentSection';
import AttachmentsSection from './components/FormSections/AttachmentsSection';
import FormFooter from './components/FormFooter';
import CustomFieldsSection from './components/FormSections/CustomFieldsSection';
import Helpers from '@Apis/helpers';
import './style.scss';

const FormBody = ({ selectedExpense, callback }) => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.UserReducers);
  const { taxes } = useSelector((state) => state.TaxReducers);
  const { cfV2DocExpense } = useSelector((state) => state.CFV2Reducers);

  const createExpense = useCallback(
    (payload, cb) => dispatch(ExpenseActions.createExpense(payload, cb)),
    [dispatch]
  );
  const updateExpense = useCallback(
    (payload, cb) => dispatch(ExpenseActions.updateExpense(payload, cb)),
    [dispatch]
  );
  const getRoles = useCallback(
    (tenantId, tenantDepartmentId) =>
      dispatch(UserActions.getRoles(tenantId, tenantDepartmentId)),
    [dispatch]
  );
  const getDocCFV2 = useCallback(
    (payload, cb) => dispatch(CfV2Actions.getDocCFV2(payload, cb)),
    [dispatch]
  );

  const defaultBillingAddressSate = useMemo(() => user?.tenant_info?.default_billing_address_info?.state, [user]);

  const {
    title,
    fileList,
    remark,
    expenseDate,
    expenseAccount,
    assetAccount,
    department,
    tenantDepartmentId,
    amount,
    taxId,
    taxInfo,
    selectedSeller,
    selectedCustomer,
    expNumber,
    docSeqId,
    initialExpNumber,
    selectedTenant,
    expenseTallyLedgerName,
    expenseTallyTransactionType,
    paidViaTallyLedgerName,
    paidViaTallyTransactionType,
    updateDocumentReason,
    cfExpenseDoc,
    isUserReadyInitial,
    useForLandedCost,
    isInterStateTax,
    referenceNumber,
    referenceDate,
    natureOfTransaction,
    natureOfAdjustment,
    additionalNatureOfAdjustment,
    expenseTaxableType,
    expenseNatureOfTransaction,
    paidViaTaxableType,
    paidViaNatureOfTransaction,
    setFormState,
  } = useExpenseForm();

  const isDataValid = () => {
    let isValid = true;
    if (
      !expNumber ||
      !title ||
      !department ||
      !expenseAccount ||
      !assetAccount ||
      !expenseDate ||
      !amount ||
      !expenseTallyTransactionType ||
      !paidViaTallyTransactionType
    ) {
      isValid = false;
    }
    return isValid;
  };

  const addExpense = (approval) => {
    setFormState({
      formSubmitted: true,
      buttonClick: approval ? 'APPROVE' : 'DRAFT',
    });

    if (
      cfExpenseDoc?.filter(
        (customField) =>
          customField.isActive &&
          customField.isRequired &&
          (customField?.fieldType === 'ATTACHMENT'
            ? !customField?.fieldValue?.length
            : !customField?.fieldValue)
      )?.length
    ) {
      notification.open({
        message: 'Please fill all the mandatory inputs',
        duration: 4,
        placement: 'top',
        type: 'error',
      });
      return;
    }

    if (isDataValid()) {
      if (selectedExpense) {
        const payload = {
          expense_id: selectedExpense?.expense_id,
          paid_via_id: assetAccount?.account_id,
          expense_date: expenseDate,
          expense_title: title,
          expense_account: expenseAccount?.account_name,
          expense_account_id: expenseAccount?.account_id,
          remarks: remark,
          amount_without_tax: amount,
          tax_id: taxId,
          tax_group_info: taxInfo,
          is_inter_state_document: isInterStateTax,
          status: approval ? 'ISSUED' : 'DRAFT',
          customer_id: selectedCustomer,
          seller_id: selectedSeller,
          department_id: department,
          tenant_department_id: tenantDepartmentId,
          attachments:
            fileList?.map((attachment) => ({
              url:
                attachment?.response?.response?.location || attachment?.url,
              type: attachment.type,
              name: attachment.name,
              uid: attachment.uid,
            })) || [],
          tenant_id: selectedTenant,
          org_id: user?.tenant_info?.org_id,
          exp_number: expNumber || '',
          is_landed_cost: useForLandedCost,
          expense_account_ledger: expenseTallyLedgerName,
          expense_account_transaction_type: expenseTallyTransactionType,
          paid_via_ledger: paidViaTallyLedgerName,
          paid_via_transaction_type: paidViaTallyTransactionType,
          custom_fields: CustomFieldHelpers.postCfStructure(cfExpenseDoc),
          update_document_reason: updateDocumentReason,
          reference_number: referenceNumber,
          reference_date: referenceDate
            ? ISTDateFormat(referenceDate, 'MM/DD/YYYY')
            : null,
          nature_of_transaction: natureOfTransaction,
          nature_of_adjustment: natureOfAdjustment,
          additional_nature_of_adjustment: additionalNatureOfAdjustment,
          debit_taxable_type: expenseTaxableType,
          debit_nature_of_transaction: expenseNatureOfTransaction,
          credit_taxable_type: paidViaTaxableType,
          credit_nature_of_transaction: paidViaNatureOfTransaction,
        };
        updateExpense(payload, () => {
          callback?.();
        });
      } else {
        const payload = {
          paid_via_id: assetAccount?.account_id,
          expense_date: expenseDate,
          expense_title: title,
          expense_account: expenseAccount?.account_name,
          expense_account_id: expenseAccount?.account_id,
          remarks: remark,
          amount_without_tax: amount,
          tax_id: taxId,
          tax_group_info: taxInfo,
          is_inter_state_document: isInterStateTax,
          status: approval ? 'ISSUED' : 'DRAFT',
          customer_id: selectedCustomer,
          seller_id: selectedSeller,
          department_id: department,
          tenant_department_id: tenantDepartmentId,
          attachments:
            fileList?.map((attachment) => ({
              url:
                attachment?.response?.response?.location || attachment?.url,
              type: attachment.type,
              name: attachment.name,
              uid: attachment.uid,
            })) || [],
          tenant_id: selectedTenant,
          org_id: user?.tenant_info?.org_id,
          seq_id: docSeqId || null,
          exp_number:
            initialExpNumber?.toLowerCase()?.trim() ===
            expNumber?.toLowerCase()?.trim()
              ? null
              : expNumber,
          is_landed_cost: useForLandedCost,
          expense_account_ledger: expenseTallyLedgerName,
          expense_account_transaction_type: expenseTallyTransactionType,
          paid_via_ledger: paidViaTallyLedgerName,
          paid_via_transaction_type: paidViaTallyTransactionType,
          custom_fields: CustomFieldHelpers.postCfStructure(cfExpenseDoc),
          reference_number: referenceNumber,
          reference_date: referenceDate
            ? ISTDateFormat(referenceDate, 'MM/DD/YYYY')
            : null,
          nature_of_transaction: natureOfTransaction,
          nature_of_adjustment: natureOfAdjustment,
          additional_nature_of_adjustment: additionalNatureOfAdjustment,
          debit_taxable_type: expenseTaxableType,
          debit_nature_of_transaction: expenseNatureOfTransaction,
          credit_taxable_type: paidViaTaxableType,
          credit_nature_of_transaction: paidViaNatureOfTransaction,
        };
        createExpense(payload, () => {
          callback?.();
        });
      }
    }
  };

  useEffect(() => {
    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName: 'EXPENSE',
    };
    getDocCFV2(payload);
  }, [user]);

  useEffect(() => {
    if (taxes?.data?.[0]?.tax_id) {
      setFormState({
        taxId: taxes?.data?.[0]?.tax_id,
        taxInfo: {
          tax_id: taxes?.data?.[0]?.tax_id, tax_name: taxes?.data?.[0]?.tax_name, tax_value: taxes?.data?.[0]?.tax_value, org_id: user?.tenant_info?.org_id, is_active: true,
        },
      });
    }
  }, [taxes]);

  useEffect(() => {
    if (cfV2DocExpense?.data?.success && !isUserReadyInitial && !selectedExpense) {
      setFormState({
        cfExpenseDoc: CustomFieldHelpers.getCfStructure(cfV2DocExpense?.data?.document_custom_fields, true) || [],
        isUserReadyInitial: true,
      });
    }

    if (selectedExpense?.expense_id && cfV2DocExpense?.data?.success && !isUserReadyInitial) {
      // Already Used Custom  Fields
      const oldCustomField = selectedExpense?.custom_fields || [];
      setFormState({
        cfExpenseDoc: CustomFieldHelpers.mergeCustomFields(cfV2DocExpense?.data?.document_custom_fields, oldCustomField, true) || [],
        isUserReadyInitial: true,
      });
    }
  }, [cfV2DocExpense]);

  useEffect(() => {
    if (selectedExpense) {
      setFormState({
        expNumber: selectedExpense?.exp_number || '',
        title: selectedExpense?.expense_title,
        selectedTenant: selectedExpense?.tenant_id,
        department: selectedExpense?.department_id,
        tenantDepartmentId: selectedExpense?.tenant_department_id,
        expenseAccount: {
          account_id: selectedExpense?.expense_account_info?.account_id,
          account_name: selectedExpense?.expense_account_info?.account_name,
        },
        expenseTallyLedgerName: selectedExpense?.expense_account_ledger,
        expenseTallyTransactionType: selectedExpense?.expense_account_transaction_type,
        assetAccount: {
          account_id: selectedExpense?.paid_via_account_info?.account_id,
          account_name: selectedExpense?.paid_via_account_info?.account_name,
        },
        paidViaTallyLedgerName: selectedExpense?.paid_via_ledger,
        paidViaTallyTransactionType: selectedExpense?.paid_via_transaction_type,
        expenseDate: dayjs(selectedExpense?.expense_date),
        amount: selectedExpense?.amount_without_tax,
        selectedSeller: selectedExpense?.seller_id,
        selectedCustomer: selectedExpense?.customer_id,
        taxId: selectedExpense?.tax_id,
        taxInfo: selectedExpense?.tax_group_info,
        remark: selectedExpense?.remarks,
        useForLandedCost: selectedExpense?.is_landed_cost,
        fileList: selectedExpense?.attachments,
        isInterStateTax: selectedExpense?.is_inter_state_document,
        childTaxes: Helpers.computeTaxation(selectedExpense?.amount_without_tax, selectedExpense?.tax_group_info, defaultBillingAddressSate, selectedExpense?.is_inter_state_document ? 'other state' : defaultBillingAddressSate)?.tax_info?.child_taxes,
        referenceNumber: selectedExpense?.reference_number || '',
        referenceDate: selectedExpense?.reference_date ? dayjs(selectedExpense?.reference_date) : null,
        natureOfTransaction: selectedExpense?.nature_of_transaction || null,
        natureOfAdjustment: selectedExpense?.nature_of_adjustment || null,
        additionalNatureOfAdjustment: selectedExpense?.additional_nature_of_adjustment || null,
        expenseTaxableType: selectedExpense?.debit_taxable_type || '',
        expenseNatureOfTransaction: selectedExpense?.debit_nature_of_transaction || '',
        paidViaTaxableType: selectedExpense?.credit_taxable_type || '',
        paidViaNatureOfTransaction: selectedExpense?.credit_nature_of_transaction || '',
      });
    }
  }, [selectedExpense]);

  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj);
        reader.onload = () => resolve(reader.result);
        reader.onerror = (error) => reject(error);
      });
    }
    setFormState({
      previewImage: file.url || file.preview,
      previewVisible: true,
      previewTitle:
        file.name || file.url.substring(file.url.lastIndexOf('/') + 1),
    });
  };

  function customInputChange(fieldValue, cfId) {
    const newCustomField = cfExpenseDoc.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url:
                attachment?.response?.response?.location || attachment?.url,
              type: attachment.type,
              name: attachment.name,
              uid: attachment.uid,
            })),
          };
        }
        return {
          ...customField,
          fieldValue,
        };
      }
      return {
        ...customField,
      };
    });
    setFormState({
      cfExpenseDoc: newCustomField,
    });
  }

  const expenseAddonEnabled = user?.tenant_info?.purchase_config?.addons?.expense?.is_active;

  return (
    <div className="expense-form__wrapper">
      <Fragment>
        <div style={{ marginBottom: '41px' }}>
          <div className="ant-row">
            <div className="ant-col-md-24 ant-col-xs-24 expense-form__section">
              <div className="ant-row">
                <BasicInfoSection />
                <ExpenseAccountSection />
                <PaidViaSection />
                <AmountAndTaxSection />
                <SellerAndCustomerSection />
                <ReferenceAndAdjustmentSection />
                <CustomFieldsSection onCustomFieldChange={customInputChange} />
                <AttachmentsSection onPreview={handlePreview} onCustomFieldChange={customInputChange} />
              </div>
            </div>
          </div>
        </div>

        <div className="custom-drawer__footer">
          <FormFooter
            expense={expenseAddonEnabled}
            onSaveDraft={() => addExpense(false)}
            onSaveAndIssue={() => addExpense(true)}
          />
        </div>
      </Fragment>

      {/* Preview Modal handled inside AttachmentsSection */}
    </div>
  );
};

const ExpenseFormRefactored = ({ selectedExpense, callback }) => {
  const dispatch = useDispatch();
  const { user, MONEY } = useSelector((state) => state.UserReducers);
  const { createExpenseLoading, updateExpenseLoading } = useSelector((state) => state.ExpenseReducers);
  const { taxes } = useSelector((state) => state.TaxReducers);
  const { cfV2DocExpense } = useSelector((state) => state.CFV2Reducers);

  const getRoles = useCallback(
    (tenantId, tenantDepartmentId) =>
      dispatch(UserActions.getRoles(tenantId, tenantDepartmentId)),
    [dispatch]
  );

  return (
    <ExpenseFormProvider
      user={user}
      selectedExpense={selectedExpense}
      taxes={taxes}
      cfV2DocExpense={cfV2DocExpense}
      valueOverrides={{ MONEY, createExpenseLoading, updateExpenseLoading, getRoles }}
    >
      <FormBody selectedExpense={selectedExpense} callback={callback} />
    </ExpenseFormProvider>
  );
};

export default ExpenseFormRefactored;
