/* eslint-disable no-restricted-syntax */
/* eslint-disable no-continue */
/* eslint-disable no-console */
import React, { Fragment, useEffect, useRef } from 'react';
import { DatePicker } from 'antd';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faChevronUp } from '@fortawesome/free-solid-svg-icons';
import PropTypes from 'prop-types';
import dayjs from 'dayjs';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import WarehouseLocationSelector from '../../../../Common/Selector/WarehouseLocationSelector';
import './style.scss';

const InwardsBatch = ({
  item, formSubmitted, grnTableData, destDepartmentId, enabledWarehouseLocations, showLineBatches, selectedLineInfo, availableBatches, updateGrnTableData, filteredBatches, isBulkUploaded, org, isBatchValidValue, setIsBatchValid, hideCost, disableCostPrice, user, priceMasking, isStockAdjustment, isApInvoiceEnabled,
}) => {
  const batchConfig = org?.organisation?.[0]?.inventory_config?.settings?.batch_management;

  function validateBatchDetailsSimple(config, batches) {
    // Helper function to check if a field is missing when it's mandatory
    const isFieldValid = (isEnabled, isMandatory, value) => {
      // If the field is enabled and mandatory, check if the value is valid
      if (isEnabled && isMandatory) {
        // return !(value === undefined || value === '' || value === 0);
        return !(value === undefined || value === '');
      }
      // If the field is not enabled or not mandatory, it's considered valid
      return true;
    };

    // Iterate through each batch
    for (const batch of batches) {
      const batchCheck = batch?.selectedBatch;
      // If product_type is "NON_STORABLE" for the current batch, move to the next batch
      if (!['NON_STORABLE', 'SERVICE'].includes(batch?.product_sku_info?.product_type)) {
        // Validate each mandatory field for the current batch
        if (
          !isFieldValid(config?.cost_price_is_enabled, config?.cost_price_is_mandatory, batchCheck?.cost_price)
          || !isFieldValid(true, true, batchCheck?.custom_batch_number)
          || !isFieldValid(config?.lot_number_is_enabled, config?.lot_number_is_mandatory, batchCheck?.lot_number)
          || !isFieldValid(config?.selling_price_is_enabled, config?.selling_price_is_mandatory, batchCheck?.selling_price)
          || !isFieldValid(config?.mrp_is_enabled, config?.mrp_is_mandatory, batchCheck?.mrp)
          || !isFieldValid(config?.ar_number_is_enabled, config?.ar_number_is_mandatory, batchCheck?.ar_number)
          || !isFieldValid(config?.roll_no_is_enabled, config?.roll_no_is_mandatory, batchCheck?.roll_no)
          || !isFieldValid(config?.freight_cost_is_enabled, (hideCost ? false : config?.freight_cost_is_mandatory), batchCheck?.freight_cost)
          || !isFieldValid(config?.other_cost_is_enabled, (hideCost ? false : config?.other_cost_is_mandatory), batchCheck?.other_cost)
          || !isFieldValid(config?.brand_is_enabled, config?.brand_is_mandatory, batchCheck?.brand)
          || !isFieldValid(config?.mfg_batch_no_is_enabled, config?.mfg_batch_no_is_mandatory, batchCheck?.mfg_batch_no)
          || !isFieldValid(config?.manufacturing_date_is_enabled, config?.manufacturing_date_is_mandatory, batchCheck?.manufacturing_date)
          // Include other conditions as needed
        ) {
          // If any field validation fails for this batch, return false
          return false;
        }
      }
    }

    // If all batches pass validation, return true
    return true;
  }

  function isAnyFieldEnabled(config, fields) {
    // Iterate over the array of fields
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      // Check if the current field is enabled in the config
      if (config?.[`${field}_is_mandatory`]) {
        // Return true as soon as one enabled field is found
        return true;
      }
    }
    // Return false if no fields are enabled
    return false;
  }

  const previousGrnData = useRef(grnTableData);

  useEffect(() => {
    if (JSON.stringify(previousGrnData.current) !== JSON.stringify(grnTableData) || isStockAdjustment) {
      previousGrnData.current = grnTableData;
      const filterGrnData = grnTableData?.filter((item) => item?.reconciliationType !== 'OUTWARD');
      const isValid = validateBatchDetailsSimple(batchConfig, filterGrnData);
      if(isValid != isBatchValidValue) {
        setIsBatchValid(isValid);
      }
    }
  }, [grnTableData, batchConfig]);

  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

  const batchConfigs = [
    {
      name: 'Cost Price',
      input_type: 'number',
      is_enabled: true,
      is_mandatory: batchConfig?.cost_price_is_mandatory,
      is_disabled: item?.selectedBatch?.batch_id || disableCostPrice || isApInvoiceEnabled,
      value: item?.selectedBatch?.cost_price ?? '',
      key: 'cost_price',
      hideCost: true,
      hideInput: isDataMaskingPolicyEnable && isHideCostPrice,
      popOverMessage: "You don't have access to view cost price",
    },
    {
      name: 'Lot #',
      input_type: 'text',
      is_enabled: batchConfig?.lot_number_is_enabled,
      is_mandatory: batchConfig?.lot_number_is_mandatory,
      is_disabled: false,
      value: item?.selectedBatch?.lot_number,
      key: 'lot_number',
      hideCost: true,
    },
    {
      name: 'MRP',
      input_type: 'number',
      is_enabled: batchConfig?.mrp_is_enabled,
      is_mandatory: batchConfig?.mrp_is_mandatory,
      is_disabled: false,
      value: item?.selectedBatch?.mrp,
      key: 'mrp',
      hideCost: true,
    },
    {
      name: 'Selling Price',
      input_type: 'number',
      is_enabled: batchConfig?.selling_price_is_enabled,
      is_mandatory: batchConfig?.selling_price_is_mandatory,
      is_disabled: false,
      value: item?.selectedBatch?.selling_price,
      key: 'selling_price',
      hideCost: true,
      hideInput: isDataMaskingPolicyEnable && isHideSellingPrice,
      popOverMessage: "You don't have access to view selling price",
    },
    {
      name: 'AR Number',
      input_type: 'text',
      is_enabled: batchConfig?.ar_number_is_enabled,
      is_mandatory: batchConfig?.ar_number_is_mandatory,
      is_disabled: false,
      value: item?.selectedBatch?.ar_number,
      key: 'ar_number',
      hideCost: true,
    },
    {
      name: 'Roll No',
      input_type: 'text',
      is_enabled: batchConfig?.roll_no_is_enabled,
      is_mandatory: batchConfig?.roll_no_is_mandatory,
      is_disabled: false,
      value: item?.selectedBatch?.roll_no,
      key: 'roll_no',
      hideCost: true,
    },
    {
      name: 'Freight Cost',
      input_type: 'number',
      is_enabled: batchConfig?.freight_cost_is_enabled,
      is_mandatory: batchConfig?.freight_cost_is_mandatory,
      is_disabled: false,
      value: item?.selectedBatch?.freight_cost,
      key: 'freight_cost',
      hideCost: !hideCost,
    },
    {
      name: 'Other Cost',
      input_type: 'number',
      is_enabled: batchConfig?.other_cost_is_enabled,
      is_mandatory: batchConfig?.other_cost_is_mandatory,
      is_disabled: false,
      value: item?.selectedBatch?.other_cost,
      key: 'other_cost',
      hideCost: !hideCost,
    },
    {
      name: 'Brand',
      input_type: 'text',
      is_enabled: batchConfig?.brand_is_enabled,
      is_mandatory: batchConfig?.brand_is_mandatory,
      is_disabled: false,
      value: item?.selectedBatch?.brand,
      key: 'brand',
      hideCost: true,
    },
    {
      name: 'Mfg Batch No',
      input_type: 'text',
      is_enabled: batchConfig?.mfg_batch_no_is_enabled,
      is_mandatory: batchConfig?.mfg_batch_no_is_mandatory,
      is_disabled: false,
      value: item?.selectedBatch?.mfg_batch_no,
      key: 'mfg_batch_no',
      hideCost: true,
    },
    {
      name: 'Mfg Date',
      input_type: 'date',
      is_enabled: batchConfig?.manufacturing_date_is_enabled,
      is_mandatory: batchConfig?.manufacturing_date_is_mandatory,
      is_disabled: false,
      value: item?.selectedBatch?.manufacturing_date ? dayjs(item?.selectedBatch?.manufacturing_date) : '',
      key: 'manufacturing_date',
      hideCost: true,
      manufacturingDateFormat: item?.manufacturingDateFormat,
    },
  ];

  const mandatoryConfigs = [
    {
      name: 'Batch #',
      input_type: 'text',
      is_enabled: true,
      is_mandatory: true,
      is_disabled: item?.selectedBatch?.batch_id,
      value: (item?.selectedBatch?.custom_batch_number || item?.selectedBatch?.batch_number) ? (item?.selectedBatch?.custom_batch_number || item?.selectedBatch?.batch_number) : '',
      key: 'custom_batch_number',
      hideCost: true,
    },
  ];

  for (let i = 0; i < batchConfigs?.length; i++) {
    if (batchConfigs[i]?.is_mandatory) {
      mandatoryConfigs.push(batchConfigs[i]);
    }
  }

  return (
    <Fragment>
      {!['NON_STORABLE', 'SERVICE'].includes(item?.product_sku_info?.product_type) ? (
        <Fragment>
          <div
            style={{
              width: '240px',
            }}
            className="create-grn-table__batch-input"
          >
            {mandatoryConfigs?.length > 0 && mandatoryConfigs?.map((innerConfig) => (
              innerConfig?.is_enabled && innerConfig?.hideCost && (
                innerConfig?.input_type === 'date' ? (
                  <div
                    className={`create-grn-table__input-wrapper ${isBulkUploaded ? 'disabled-element' : ''}`}
                    style={{ marginTop: '3px' }}
                  >
                    <H3Text
                      text="Mfg Date"
                      className="create-grn-table__input-label"
                      required={innerConfig?.is_mandatory}
                    />
                    &nbsp;
                    <DatePicker
                      value={innerConfig?.value}
                      onChange={(value) => {
                        const copyData = JSON.parse(JSON.stringify(grnTableData));

                        const updatedData = copyData.map((obj) => {
                          if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.key && item?.key)) {
                            const newSelectedBatch = { ...JSON.parse(JSON.stringify(obj?.selectedBatch)), [innerConfig?.key]: value };
                            return {
                              ...obj,
                              selectedBatch: newSelectedBatch,
                            };
                          }
                          return obj;
                        });
                        updateGrnTableData(updatedData);
                      }}
                      format={innerConfig?.manufacturingDateFormat}
                      picker={innerConfig?.manufacturingDateFormat !== 'DD/MM/YYYY' ? 'month' : ''}
                      className={`create-grn-table__input-date ${(formSubmitted && innerConfig?.is_mandatory && !innerConfig?.value) ? 'orgFormInputError' : ''}`}
                    />
                  </div>
                ) : (
                  <div
                    className={innerConfig?.name === 'Cost Price' && isApInvoiceEnabled ? 'display-none' : `create-grn-table__input-wrapper ${isBulkUploaded ? 'disabled-element' : ''}`}
                    //  className="create-grn-table__input-wrapper"
                    style={{ marginTop: '3px' }}
                  >
                    <H3Text
                      text={innerConfig?.name}
                      className="create-grn-table__input-label"
                      required={innerConfig?.is_mandatory}
                    />
                    &nbsp;
                    <H3FormInput
                      value={innerConfig?.value ?? ''}
                      type={innerConfig?.input_type}
                      labelClassName="orgFormLabel"
                      inputClassName={`orgFormInput ${(formSubmitted && innerConfig?.is_mandatory && (!innerConfig?.value && innerConfig?.value !== 0)) ? 'orgFormInputError' : ''} `}
                      onChange={(event) => {
                        const copyData = JSON.parse(JSON.stringify(grnTableData));
                        const updatedData = copyData.map((obj) => {
                          if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.key && item?.key)) {
                            const newSelectedBatch = {
                              ...JSON.parse(JSON.stringify(obj?.selectedBatch)),
                              [innerConfig?.key]: event?.target?.value,
                            };
                            return {
                              ...obj,
                              // available_batches: [newSelectedBatch, ...obj?.available_batches?.filter((i) => i?.batch_id)],
                              selectedBatch: newSelectedBatch,
                            };
                          }
                          return obj;
                        });
                        updateGrnTableData(updatedData);
                      }}
                      disabled={innerConfig?.is_disabled}
                      hideInput={innerConfig?.hideInput}
                      popOverMessage={innerConfig?.popOverMessage}
                    />
                  </div>
                )
              )
            ))}
            {((item?.expiryDays || item?.expiry_days) >= 0) && (
              <div
                // className="create-grn-table__input-wrapper"
                className={`create-grn-table__input-wrapper ${isBulkUploaded ? 'disabled-element' : ''}`}
                style={{ marginTop: '3px' }}
              >
                <H3Text text="Expiry" className="create-grn-table__input-label" />
                &nbsp;
                {/* {item?.selectedBatch?.expiry_date?.toString()} */}
                <DatePicker
                  value={item?.selectedBatch?.expiry_date ? dayjs(item?.selectedBatch?.expiry_date) : null}
                  onChange={(value) => {
                    const copyData = JSON.parse(JSON.stringify(grnTableData));

                    const updatedData = copyData.map((obj) => {
                      if (
                        (obj.po_line_id === item.po_line_id && obj?.po_line_id)
                        || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id)
                        || (obj?.key === item?.key && item?.key)
                      ) {
                        const newSelectedBatch = {
                          ...JSON.parse(JSON.stringify(obj?.selectedBatch)),
                          expiry_date: value,
                        };
                        return {
                          ...obj,
                          selectedBatch: newSelectedBatch,
                        };
                      }
                      return obj;
                    });
                    updateGrnTableData(updatedData);
                  }}
                  format={item?.expiryDateFormat}
                  picker={item?.expiryDateFormat !== 'DD/MM/YYYY' ? 'month' : undefined}
                  className="create-grn-table__input-date"
                />
              </div>
            )}
            {enabledWarehouseLocations && (
              <div
                className={`create-grn-table__input-wrapper ${isBulkUploaded ? 'disabled-element' : ''}`}
                // className="create-grn-table__input-wrapper"
                style={{ marginTop: '3px' }}
              >
                <H3Text
                  text="Shelf/Rack"
                  className="create-grn-table__input-label"
                  required={batchConfig?.inventory_location_is_mandatory}

                />
                <WarehouseLocationSelector
                  containerClassName="create-grn-table__input-select"
                  hideTitle
                  selectedInventoryLocation={item?.selectedBatch?.inventory_location_path}
                  onChange={(id, name) => {
                    const copyData = JSON.parse(JSON.stringify(grnTableData));
                    const updatedData = copyData.map((obj) => {
                      if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.key && item?.key)) {
                        const newSelectedBatch = {
                          ...JSON.parse(JSON.stringify(obj?.selectedBatch)),
                          inventory_location_id: id,
                          inventory_location_path: name,
                        };
                        return {
                          ...obj,
                          // available_batches: [newSelectedBatch, ...obj?.available_batches?.filter((i) => i?.batch_id)],
                          selectedBatch: newSelectedBatch,
                        };
                      }
                      return obj;
                    });
                    updateGrnTableData(updatedData);
                  }}
                  destDepartmentId={destDepartmentId}
                />
              </div>
            )}
            {item?.showMoreBatchInfo && (
              <Fragment>
                {batchConfigs?.filter((item) => !item?.is_mandatory)?.map((innerConfig) => (
                  innerConfig?.is_enabled && innerConfig?.hideCost && (
                    innerConfig?.input_type === 'date' ? (
                      <div
                        className={`create-grn-table__input-wrapper ${isBulkUploaded ? 'disabled-element' : ''}`}
                        style={{ marginTop: '3px' }}
                      >
                        <H3Text
                          text="Mfg Date"
                          className="create-grn-table__input-label"
                          required={batchConfig?.manufacturing_date_is_mandatory}
                        />
                        &nbsp;
                        <DatePicker
                          value={item?.selectedBatch?.manufacturing_date ? dayjs(item?.selectedBatch?.manufacturing_date) : ''}
                          onChange={(value) => {
                            const copyData = JSON.parse(JSON.stringify(grnTableData));

                            const updatedData = copyData.map((obj) => {
                              if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.key && item?.key)) {
                                const newSelectedBatch = { ...JSON.parse(JSON.stringify(obj?.selectedBatch)), manufacturing_date: value };
                                return {
                                  ...obj,
                                  selectedBatch: newSelectedBatch,
                                };
                              }
                              return obj;
                            });
                            updateGrnTableData(updatedData);
                          }}
                          format={innerConfig?.manufacturingDateFormat}
                          picker={innerConfig?.manufacturingDateFormat !== 'DD/MM/YYYY' ? 'month' : ''}
                          className={`create-grn-table__input-date ${(formSubmitted && batchConfig?.manufacturing_date_is_mandatory && !item.selectedBatch?.manufacturing_date) ? 'orgFormInputError' : ''}`}
                        />
                      </div>
                    ) : (
                      <div
                        className={`create-grn-table__input-wrapper ${isBulkUploaded ? 'disabled-element' : ''}`}
                        //  className="create-grn-table__input-wrapper"
                        style={{ marginTop: '3px' }}
                      >
                        <H3Text
                          text={innerConfig?.name}
                          className="create-grn-table__input-label"
                          required={innerConfig?.is_mandatory}
                        />
                        &nbsp;
                        <H3FormInput
                          value={innerConfig?.value ? innerConfig?.value : ''}
                          type={innerConfig?.input_type}
                          labelClassName="orgFormLabel"
                          inputClassName={`orgFormInput ${(formSubmitted && innerConfig?.is_mandatory && !innerConfig?.value) ? 'orgFormInputError' : ''} `}
                          onChange={(event) => {
                            const copyData = JSON.parse(JSON.stringify(grnTableData));
                            const updatedData = copyData.map((obj) => {
                              if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.key && item?.key)) {
                                const newSelectedBatch = {
                                  ...JSON.parse(JSON.stringify(obj?.selectedBatch)),
                                  [innerConfig?.key]: event?.target?.value,
                                };
                                return {
                                  ...obj,
                                  // available_batches: [newSelectedBatch, ...obj?.available_batches?.filter((i) => i?.batch_id)],
                                  selectedBatch: newSelectedBatch,
                                };
                              }
                              return obj;
                            });
                            updateGrnTableData(updatedData);
                          }}
                          hideInput={innerConfig?.hideInput}
                          popOverMessage={innerConfig?.popOverMessage}
                        />
                      </div>
                    )
                  )
                ))}
              </Fragment>
            )}
          </div>
          <div className="flex-display">
            <div
              onClick={() => {
                const copyData = JSON.parse(JSON.stringify(grnTableData));
                const updatedData = copyData.map((obj) => {
                  if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.key && item?.key)) {
                    return { ...obj, showMoreBatchInfo: !obj?.showMoreBatchInfo };
                  }
                  return obj;
                });
                updateGrnTableData(updatedData);
              }}
              className="more-batch-info"
              style={{
                display: 'flex',
                alignItems: 'center',
                width: '100px',
              }}
            >
              <div>

                {`show ${item?.showMoreBatchInfo ? 'less' : 'more'}`}
              </div>
              &nbsp;
              {!item?.showMoreBatchInfo ? <FontAwesomeIcon icon={faChevronDown} /> : <FontAwesomeIcon icon={faChevronUp} />}
            </div>
            {!isBulkUploaded && (
              <H3Text
                text={item?.selectedBatch?.batch_id ? 'Choose Different Batch' : 'Add to Existing Batch'}
                onClick={() => {
                  showLineBatches(true);
                  availableBatches(item?.available_batches);
                  selectedLineInfo({
                    expiryDays: item?.expiryDays,
                    batchUom: item?.product_sku_info?.uom_info || item?.uom_info,
                    selectedBatch: item?.selectedBatch,
                    uomList: item?.product_sku_info?.uom_list || item?.uom_list,
                    tenantProductId: item?.product_sku_info?.tenant_product_id,
                    poLineId: item?.po_line_id,
                    costPrice: item?.offer_price,
                    mrp: item?.mrp,
                    nextBatchCode: item?.nextBatchCode,
                    key: item?.key,
                    grn_line_id: item.grn_line_id || null,
                  });
                }}
                className="create-grn__table-small-action"
              />
            )}
          </div>
        </Fragment>
      ) : '-'}
    </Fragment>
  );
};

const mapStateToProps = ({
  UserReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  org: UserReducers.org,
  priceMasking: UserReducers.priceMasking,
});

InwardsBatch.propTypes = {
  item: PropTypes.any,
  formSubmitted: PropTypes.bool,
  destDepartmentId: PropTypes.any,
  grnTableData: PropTypes.any,
  enabledWarehouseLocations: PropTypes.any,
  showLineBatches: PropTypes.any,
  availableBatches: PropTypes.any,
  selectedLineInfo: PropTypes.any,
  updateGrnTableData: PropTypes.any,
  filteredBatches: PropTypes.any,

};

export default connect(mapStateToProps)(withRouter(InwardsBatch));
