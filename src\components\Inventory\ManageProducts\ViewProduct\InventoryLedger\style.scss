.inventory-ledger__wrapper {
  .row-disabled {
    opacity: 0.7;
    background-color: #efefef;
  }

  .ledger__deleted-transaction-wrapper {
    display: flex;
    white-space: nowrap;
    align-items: center;

    .ledger__deleted-transaction {
      background-color: #ee0b4e;
      color: white;
      font-weight: 500;
      margin-left: 5px;
      border-radius: 4px;
      font-size: 11px;
      height: 16px;
      padding: 0px 4px;
    }
  }

  .link-text {
    color: #1890ff;
    cursor: pointer;
  }

  .link-text-disabled {
    color: #bbbbbb !important;
  }

  .ant-table-content {
    overflow-x: scroll;

    @media (max-width: 767px) {
      width: 100% !important;

      .ant-table-cell {
        width: 90vw;
      }
    }
  }
}

.ledger-reference-number {
  color: #2d7cf7;
  cursor: pointer;
  font-weight: 500;
}

.ledger-header {
  display: flex;
  width: 100%;

  .orgInputContainer {
    margin-bottom: 0px !important;
  }

  .ledger-header-wrapper {
    margin-left: auto;
    display: flex;
    .orgFormLabel {
      display: none;
    }
    .section-search .section-search-bar {
      padding: 0px 10px 0px 10px !important;
      border-radius: 4px;
      margin-left: 10px;
      height: 28px;
      font-size: 12px;
    }

  }
}

.ledger-txn-stock {
  color: #109610;
  font-weight: 600;
}

.ledger-txn-stock-negative {
  color: #d70404 !important;
}

.ia-type-tag {
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: 600;
  border-radius: 3px;
  padding: 2px 5px 2px 2px;
  text-align: center;
  width: 70px;
  font-size: 10.5px;
  z-index: 1;
}

.logs-header__action-btn {
  border-radius: 4px;
  padding: 5px 10px 8px 10px;
  color: #2D7DF7;
  background-color: rgba(45, 124, 247, 0.1);
  transition: 0.2s;
  height: 30px !important;

  &:hover {
    cursor: pointer;
    background-color: rgba(45, 124, 247, 0.2) !important;
  }

  height: 31px;
}


.logs-header__action-btn-disabled {
  cursor: not-allowed !important;
}

.email-submit {
  border-radius: 4px !important;
  width: 100px;
  padding: 7px 10px !important;
  margin-top: 20px;
}

.filters__buttons .action-button .action-button {
  height: 32px !important;
}

