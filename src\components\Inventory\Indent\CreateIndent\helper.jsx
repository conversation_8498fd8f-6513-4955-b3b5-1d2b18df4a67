/* eslint-disable unicorn/prefer-structured-clone */
import { INFINITE_EXPIRY_DATE, QUANTITY, toISTDate } from '@Apis/constants';
import React, { Fragment } from 'react';
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import dayjs from 'dayjs';
import Decimal from 'decimal.js';
import getStockAdjustmentErrorList from './stockAdjustmentErrors';
import { Input, Popover } from 'antd';
import PRZText from '@Components/Common/UI/PRZText';
import { CloseOutlined, InfoCircleOutlined } from '@ant-design/icons';
import BatchCustomFields from '@Components/Common/BatchCustomFields';
import InwardsBatch from '@Components/Purchase/GoodsReceiving/GRNForm/InwardsBatch';
import ProductCategoryLabel from '@Components/Common/ProductCategoryLabel';
import ProductFilterV2 from '@Components/Common/ProductFilterV2';
import { v4 as uuidv4 } from 'uuid';
import { actionTypes } from './reducer';

export const createData = ({
  dataItem, tenantSku, otherDepStocksData, userTenantDepartmentId, inventoryLocations, selectedAdjustmentType, autoPrintDescription, batchCustomFields,
}) => {
  const copyDataItem = dataItem;

  const newBatch = {
    tenant_department_id: userTenantDepartmentId,
    tenant_product_id: tenantSku?.tenant_product_id,
    expiry_date: tenantSku?.product_info?.expiry_days > 0 ? (dayjs().endOf('day').add(tenantSku?.product_info?.expiry_days, 'day')) : dayjs(INFINITE_EXPIRY_DATE, 'YYYY-MM-DD').endOf('day'),
    lot_number: '',
    custom_batch_number: `${tenantSku?.product_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${tenantSku?.product_info?.product_batch_counter}`,
    cost_price: tenantSku?.cost_price || 0,
    mrp: tenantSku?.mrp || 0,
    selling_price: tenantSku?.selling_price || 0,
    uom_id: tenantSku?.uom_id,
    manual_entry: false,
    inventory_location_id: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
    inventory_location_path: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
    is_rejected_batch: false,
    custom_fields: batchCustomFields,
  };
  const batches = tenantSku?.product_batches;
  if (tenantSku?.barcode_batch_id) {
    for (let j = 0; j < batches?.length; j++) {
      if (tenantSku?.barcode_batch_id === batches[j]?.batch_id) {
        batches[j].consumed_qty = 1;
        batches[j].batch_in_use = true;
      }
    }
  } else {
    for (let j = 0; j < batches?.length; j++) {
      batches[j].consumed_qty = 0;
      batches[j].batch_in_use = true;
    }
  }
  copyDataItem.product_sku_name = `${tenantSku?.product_info.product_sku_name}`;
  copyDataItem.otherDepartmentStock = otherDepStocksData?.tenant_products;
  copyDataItem.product_sku_id = tenantSku?.product_info.product_sku_id;
  copyDataItem.availableQuantity = tenantSku?.available_qty;
  copyDataItem.thresholdQuantity = `${tenantSku?.threshold_qty}`;
  copyDataItem.tenantProductId = `${tenantSku?.tenant_product_id}`;
  copyDataItem.asset = tenantSku?.product_info?.assets || [];
  copyDataItem.selectedBatch = newBatch;
  copyDataItem.expiryDateFormat = tenantSku?.expiry_date_format;
  copyDataItem.manufacturingDateFormat = tenantSku?.manufacturing_date_format;
  copyDataItem.available_batches = selectedAdjustmentType !== 'OUTWARD'
    ? (tenantSku?.product_batches
      ? [
        {
          ...newBatch,
        },
        ...(tenantSku?.product_batches || []).map((batch) => ({
          ...batch,
          custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
        })),
      ]
      : [
        {
          ...newBatch,
        },
      ])
    : Helpers.batchSelectionMethod(
      Helpers.getBatchMethod(tenantSku),
      (tenantSku?.product_batches || []).map((batch) => ({
        ...batch,
        custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
      })),
    );
  copyDataItem.expiry_days = tenantSku?.product_info?.expiry_days;
  copyDataItem.expiryDate = tenantSku?.product_info?.expiry_days >= 0 ? (toISTDate(dayjs()).add(tenantSku?.product_info?.expiry_days || 0, 'day')).format('YYYY-MM-DD') : INFINITE_EXPIRY_DATE;
  copyDataItem.unit = tenantSku?.product_info?.unit;
  copyDataItem.uom_info = tenantSku?.uom_info;
  copyDataItem.uom_list = tenantSku?.uom_list;
  copyDataItem.sku = tenantSku.product_info.product_sku_id || '';
  copyDataItem.expiryDays = tenantSku?.product_info?.expiry_days;
  copyDataItem.internal_sku_code = tenantSku?.product_info?.internal_sku_code;
  copyDataItem.ref_product_code = tenantSku?.product_info?.ref_product_code;
  copyDataItem.quantity = tenantSku?.barcode_batch_id ? 1 : 0;
  copyDataItem.nextBatchCode = `${tenantSku?.product_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${tenantSku?.product_info?.product_batch_counter}`;
  copyDataItem.batchConsumptionMethod = Helpers.getBatchMethod(tenantSku);
  copyDataItem.remarks = autoPrintDescription ? (otherDepStocksData?.description || '')?.replace(/<[^>]+>/g, '') : '';
  copyDataItem.product_category_info = tenantSku?.product_category_info;
  copyDataItem.secondaryUomUqc = tenantSku?.secondary_uom_info?.uqc;
  copyDataItem.secondaryAvailableQty = tenantSku?.secondary_available_qty;
  copyDataItem.secondary_uom_qty = 0;
  copyDataItem.secondaryUomId = tenantSku?.secondary_uom_id;

  return copyDataItem;
};

export const getBatchMethod = (tenantSku) => {
  let batchMethod;

  if (tenantSku?.default_outwards_method) {
    batchMethod = tenantSku?.default_outwards_method;
  } else if (tenantSku?.expiry_days > 0) {
    batchMethod = 'FEFO';
  } else {
    batchMethod = 'FIFO';
  }

  return batchMethod;
};

export function getBatches(oldBatches, quantity) {
  const batches = oldBatches;
  let remainingQty = new Decimal(quantity);

  for (let i = 0; i < batches?.length; i++) {
    batches[i].consumed_qty = 0;
    batches[i].batch_in_use = true;
  }

  for (let i = 0; i < batches?.length; i++) {
    const availableQty = new Decimal(batches[i]?.available_qty || 0);
    const consumedQty = new Decimal(batches[i]?.consumed_qty || 0);

    if (remainingQty.gt(0) && consumedQty.lt(availableQty)) {
      const qtyToConsume = Decimal.min(availableQty, remainingQty);
      batches[i].consumed_qty = qtyToConsume.toNumber();
      remainingQty = remainingQty.minus(qtyToConsume);
    } else {
      batches[i].consumed_qty = 0;
      remainingQty = remainingQty.minus(new Decimal(batches[i].consumed_qty));
    }
    batches[i].batch_in_use = true;
  }
  return batches;
}

export function isDataValid({ data, selectedAdjustmentType, allStock }) {
  let isDataValid = true;
  for (const datum of data) {
    const cfValidBatchLevel = !CustomFieldHelpers.isCfValid(datum?.selectedBatch?.custom_fields);
    if ((selectedAdjustmentType === 'RECONCILIATION' ? datum.quantity < 0 : !datum.quantity) || !datum.product_sku_name || (datum?.sku
      && (
        (selectedAdjustmentType
          && (selectedAdjustmentType === 'OUTWARD'
            ? Number(Number(Helpers.getValueTotalInObject(datum?.available_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'available_qty') || 0) - Number(datum?.quantity || 0)) < 0
            : Number(Number(Helpers.getValueTotalInObject(datum?.available_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'available_qty') || 0) + Number(datum?.quantity || 0) < 0))
        )
        || (datum?.quantity < 0) || (selectedAdjustmentType === 'INWARDS' && !datum?.expiryDate)
        || (selectedAdjustmentType === 'INWARDS' && !datum?.selectedBatch)
        || ((['INWARDS'].includes(selectedAdjustmentType) || datum?.reconciliationType === 'INWARDS') && cfValidBatchLevel)
      ))
    ) {
      isDataValid = false;
    }
  }
  return isDataValid;
}

export function getDataSource(data) {
  const copyData = JSON.parse(JSON.stringify(data));
  return [...(copyData || []).filter((item) => item?.product_sku_id), ...(copyData || []).filter((item) => !item?.product_sku_id)];
}

export function onBulkBatch(oldBatches, line, user) {
  const batches = oldBatches;
  const payload = {
    batch_id: null,
    tenant_product_id: line?.tenant_product?.[0]?.tenant_product_id,
    available_qty: line?.quantity,
    expiry_date: line?.expiry_days > 0 ? dayjs(line?.expiry_days).format('YYYY-MM-DD') : INFINITE_EXPIRY_DATE,
    lot_number: line?.lotNumber?.toString() || '',
    custom_batch_number: `${line?.internal_sku_code}/${dayjs().format('DDMMYY')}/${line?.product_batch_counter}`,
    cost_price: line?.tenant_product?.[0]?.cost_price || 0,
    selling_price: 0,
    tenant_department_id: Helpers.getTenantDepartmentId(user, null, null, null) || user?.tenant_info?.default_store_id,
    uom_id: line?.uom_info?.uom_id,
    manual_entry: true,
  };
  return { batches: [...batches, payload], selectedBatch: payload };
}

export function getFooterMessage(selectedAdjustmentType) {

  let footerMessage = '';
  if (selectedAdjustmentType === 'OUTWARD') {
    footerMessage = 'For Outwards bulk adjustments, you can not add the same product multiple times ';
  } else if (selectedAdjustmentType === 'INWARDS') {
    footerMessage = 'For Inwards bulk adjustments, you can not edit the product details';
  } else {
    footerMessage = 'For Reconciliation bulk adjustments, you can not edit the product details and you can not add the same product multiple times ';
  }

  return footerMessage;
}

export function isLineQuantityHasError({ record, selectedAdjustmentType, allStock, formSubmitted }) {
  if ((selectedAdjustmentType === 'OUTWARD' || selectedAdjustmentType === 'RECONCILIATION' && ((record?.newQuantity || 0) <= Number(record?.availableQty)) || Number(record?.quantity) <= 0) && formSubmitted && ((Number(Number(Helpers.getValueTotalInObject(record?.available_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'available_qty') || 0) - Number(record?.quantity || 0)) < 0 || Number(Helpers.getValueTotalInObject(record?.available_batches?.filter((item) => item?.batch_in_use), 'available_qty') || 0) === 0) || Number(record?.quantity) <= 0)) {
    return true;
  } if (selectedAdjustmentType === 'INWARDS' && formSubmitted && Number(record?.quantity) <= 0) {
    return true;
  } return false;
}

const isFieldValid = (isEnabled, isMandatory, value) => {
  // If the field is enabled and mandatory, check if the value is valid
  if (isEnabled && isMandatory) {
    // return !(value === undefined || value === '' || value === 0);
    return !(value === undefined || value === '');
  }
  // If the field is not enabled or not mandatory, it's considered valid
  return true;
};

export function validateBatchDetailsSimple(config, batches, hideCost) {
  // Helper function to check if a field is missing when it's mandatory

  // Iterate through each batch
  for (const batch of batches) {
    const batchCheck = batch?.selectedBatch;
    // If product_type is "NON_STORABLE" for the current batch, move to the next batch
    if (!['NON_STORABLE', 'SERVICE'].includes(batch?.product_sku_info?.product_type) && // Validate each mandatory field for the current batch
      (
        !isFieldValid(config?.cost_price_is_enabled, config?.cost_price_is_mandatory, batchCheck?.cost_price)
        || !isFieldValid(true, true, batchCheck?.custom_batch_number)
        || !isFieldValid(config?.lot_number_is_enabled, config?.lot_number_is_mandatory, batchCheck?.lot_number)
        || !isFieldValid(config?.selling_price_is_enabled, config?.selling_price_is_mandatory, batchCheck?.selling_price)
        || !isFieldValid(config?.mrp_is_enabled, config?.mrp_is_mandatory, batchCheck?.mrp)
        || !isFieldValid(config?.ar_number_is_enabled, config?.ar_number_is_mandatory, batchCheck?.ar_number)
        || !isFieldValid(config?.roll_no_is_enabled, config?.roll_no_is_mandatory, batchCheck?.roll_no)
        || !isFieldValid(config?.freight_cost_is_enabled, (hideCost ? false : config?.freight_cost_is_mandatory), batchCheck?.freight_cost)
        || !isFieldValid(config?.other_cost_is_enabled, (hideCost ? false : config?.other_cost_is_mandatory), batchCheck?.other_cost)
        || !isFieldValid(config?.brand_is_enabled, config?.brand_is_mandatory, batchCheck?.brand)
        || !isFieldValid(config?.mfg_batch_no_is_enabled, config?.mfg_batch_no_is_mandatory, batchCheck?.mfg_batch_no)
        || !isFieldValid(config?.manufacturing_date_is_enabled, config?.manufacturing_date_is_mandatory, batchCheck?.manufacturing_date)
        // Include other conditions as needed
      )) {
      // If any field validation fails for this batch, return false
      return false;
    }
  }

  // If all batches pass validation, return true
  return true;
}

export const validateAdjustmentForm = ({
  selectedAdjustmentType,
  selectedAdjustmentReason,
  userDepartmentId,
  selectedDate,
  cfStockAdjustmentDoc,
  data,
  allStock,
  org,
  priceMasking,
  selectedJobWork,
  adjustmentBasedOn,
  isSecondaryUomEnabled,
  adjustmentNumber,
}) => {
  const { docLevelError, lineLevelError } = getStockAdjustmentErrorList({
    selectedAdjustmentType,
    selectedAdjustmentReason,
    userDepartmentId,
    selectedDate,
    cfStockAdjustmentDoc,
    data,
    isLineQuantityHasError: (value) => isLineQuantityHasError(value),
    allStock,
    org,
    priceMasking,
    selectedJobWork,
    adjustmentBasedOn,
    isSecondaryUomEnabled,
    adjustmentNumber,
  });

  const isLineBatchValid =
    selectedAdjustmentType === 'RECONCILIATION' &&
    data?.every((item) => item?.reconciliationType === 'OUTWARD');

  const checkSecondaryUomQty =
    data?.every((item) => Math.abs(item?.secondary_uom_qty) > 0) && isSecondaryUomEnabled;

  const missingRequiredCustomFields =
    cfStockAdjustmentDoc?.filter(
      (cf) =>
        cf.isActive &&
        cf.isRequired &&
        (cf.fieldType === 'ATTACHMENT'
          ? !cf?.fieldValue?.length
          : !cf?.fieldValue),
    )?.length > 0;

  const batchConfig = org?.organisation?.[0]?.inventory_config?.settings?.batch_management;

  const isBatchValid = validateBatchDetailsSimple(batchConfig, data?.filter((item) => item?.reconciliationType !== 'OUTWARD'), true);

  const isAdjustmentDataValid = isDataValid({ data, selectedAdjustmentType, allStock });

  const isValid =
    (isLineBatchValid || isBatchValid || selectedAdjustmentType === 'OUTWARD') &&
    (checkSecondaryUomQty || (!docLevelError?.length && !lineLevelError?.length && isAdjustmentDataValid)) &&
    (selectedAdjustmentType !== 'RECONCILIATION' ? selectedAdjustmentReason : true) &&
    selectedAdjustmentType &&
    selectedDate &&
    !missingRequiredCustomFields;

  return { isValid, docLevelError, lineLevelError };
};
const getOutwardsBatches = (rows, tenantProductId, uomInfo) =>
  rows
    ?.filter((row) => row?.consumed_qty > 0)
    ?.map((row) => ({
      ...row,
      quantity: -row.consumed_qty,
      tenant_product_id: tenantProductId,
      uom_id: uomInfo?.uom_id,
      mrp: row?.mrp || 0,
      is_rejected_batch: !!row?.is_rejected_batch,
      custom_fields: CustomFieldHelpers.postCfStructure(row?.custom_fields),
    }));

export const buildIndentLines = (data, adjustmentBasedOn, selectedAdjustmentType, selectedPRForAdjustment, getSellerCodesSuccess) => {

  return data?.map((item) => {
    let secondaryUomQty = 0;

    if (adjustmentBasedOn === 'AVAILABLE_STOCK') {
      secondaryUomQty =
        Number(item?.secondaryAvailableQty) < Number(item?.secondaryQuantity)
          ? Math.abs(item?.secondary_uom_qty)
          : -Math.abs(item?.secondary_uom_qty);
    } else if (adjustmentBasedOn === 'CURRENT_STOCK') {
      secondaryUomQty =
        selectedAdjustmentType === 'OUTWARD' ||
        (selectedAdjustmentType === 'RECONCILIATION' && item?.reconciliationType === 'OUTWARD')
          ? -item?.secondary_uom_qty
          : item?.secondary_uom_qty;
    }

    if (
      selectedAdjustmentType === 'OUTWARD' ||
      (selectedAdjustmentType === 'RECONCILIATION' && item?.reconciliationType === 'OUTWARD')
    ) {
      return {
        quantity: -item?.quantity,
        secondary_uom_qty: item?.secondaryUomId ? secondaryUomQty : 0,
        remarks: item?.remarks,
        seller_code: item?.seller_code,
        tenant_product_id: item?.tenantProductId,
        expiry_date: item?.expiryDate,
        uom_id: item?.uom_info?.uom_id,
        uom_info: item?.uom_info,
        pr_line_id: selectedPRForAdjustment ? item?.pr_line_id : null,
        product_batches: getOutwardsBatches(item?.available_batches, item?.tenantProductId, item?.uom_info),
        default_outwards_method: item?.batchConsumptionMethod,
        reconciliationType: item?.reconciliationType,
      };
    }

    return {
      quantity: item?.quantity,
      secondary_uom_qty: item?.secondaryUomId ? secondaryUomQty : 0,
      seller_code: item?.seller_code,
      remarks: item?.remarks,
      tenant_product_id: item?.tenantProductId,
      uom_id: item?.uom_info?.uom_id,
      uom_info: item?.uom_info,
      expiry_date: toISTDate(item?.expiryDate),
      reconciliationType: item?.reconciliationType,
      product_batches: [
        {
          ...item?.selectedBatch,
          quantity: item?.quantity,
          tenant_product_id: item?.tenantProductId,
          uom_id: item?.uom_info?.uom_id,
          custom_fields: CustomFieldHelpers.postCfStructure(item?.selectedBatch?.custom_fields),
          is_rejected_batch: item?.selectedBatch?.is_rejected_batch,
          seller_id: getSellerCodesSuccess?.data?.find((j) => j?.internal_slr_code === item?.seller_code)?.seller_id,
          seller_name: getSellerCodesSuccess?.data?.find((j) => j?.internal_slr_code === item?.seller_code)?.seller_name,
        },
      ],
    };
  });
};

export const buildPayload = ({
  type,
  reason,
  lines,
  user,
  selectedJobWork,
  selectedTenant,
  selectedDate,
  description,
  selectedPRForAdjustment,
  cfStockAdjustmentDoc,
  fileList,
  userTenantDepartmentId,
  adjustmentNumber,
  initialAdjustmentNumber,
  docSeqId,
}) => ({
  inventory_tenant_id: selectedJobWork ? selectedJobWork?.tenant_id : user?.tenant_info?.tenant_id,
  adjustment_entity_id:
    (reason === 'INVENTORY_TRANSFER' || selectedJobWork) ? selectedTenant : user?.tenant_info?.tenant_id,
  adjustment_entity_type: type,
  adjustment_reason: reason,
  remarks: description,
  adjustment_date: selectedDate,
  adjustment_lines: lines,
  purchase_requisition_id: selectedPRForAdjustment ? selectedPRForAdjustment.purchase_requisition_id : null,
  custom_fields: CustomFieldHelpers.postCfStructure(cfStockAdjustmentDoc),
  attachments:
    fileList?.map((attachment) => ({
      url: attachment?.response?.response?.location || attachment?.url,
      type: attachment.type,
      name: attachment.name,
      uid: attachment.uid,
    })) || [],
  tenant_department_id: userTenantDepartmentId,
  production_route_line_id: selectedJobWork?.production_route_line_id ?? null,
  mo_id: selectedJobWork?.mo_id ?? null,
  ia_number:
    initialAdjustmentNumber?.toLowerCase()?.trim() === adjustmentNumber?.toLowerCase()?.trim()
      ? null
      : adjustmentNumber,
  seq_id: docSeqId || null,
});

export function getColumns({
  localDispatch,
  selectedAdjustmentType,
  adjustmentBasedOn,
  formSubmitted,
  data,
  userTenantDepartmentId,
  selectedTenant,
  user,
  selectedJobWork,
  handleProductChange,
  handleProductChangeValue,
  handleMultiProductChange,
  allStock,
  isBatchValid,
  handleDelete,
  updateBatchCfs,
  isSecondaryUomEnabled,
}) {
  const columns = [
    {
      title: 'Product',
      width: '250px',
      render: (text, record) => {

        let reconciliationType = false;
        if (selectedAdjustmentType === 'RECONCILIATION') {
          reconciliationType = record?.reconciliationType;
        }

        return (
          <div
            className={`table__product-name ${record?.isBulkUploaded && selectedAdjustmentType !== 'OUTWARD'
              ? 'disabled-element'
              : ''
                }`}
          >
            <React.Fragment>
              <ProductFilterV2
                record={record}
                customClass="product_selector"
                productTypes={['STORABLE', 'SERVICE']}
                handleProductChangeValue={handleProductChangeValue}
                handleProductChange={handleProductChange}
                required
                showError={formSubmitted && !record.product_sku_name}
                onClear={(key) => {
                  const copyData = JSON.parse(JSON.stringify(data));
                  const updatedData = copyData.map((item) => {
                    if (item.key === key) {
                      return {
                        key: uuidv4(),
                        tenant_product_id: null,
                        product_sku_id: null,
                        tenantProductId: null,
                        product_sku_name: '',
                        asset: '',
                        unit: null,
                        quantity: 0,
                        availableQuantity: null,
                        sku: null,
                        selectedBatch: null,
                        internal_sku_code: null,
                      };
                    }
                    return {
                      ...item,
                    };
                  });
                  localDispatch({
                    type: actionTypes.SET_MULTIPLE_FIELDS,
                    payload: {
                      data: updatedData,
                    },
                  });
                }}
                excludedProducts={data?.map((item) => item?.tenantProductId)}
                disabled={!selectedAdjustmentType || record?.product_sku_id}
                excludeOutOfStock={selectedAdjustmentType === 'OUTWARD'}
                tenantDepartmentId={userTenantDepartmentId}
                showClear={
                  !record?.isBulkUploaded
                    && selectedAdjustmentType !== 'OUTWARD'
                }
                filterReservedQuantity={selectedAdjustmentType === 'OUTWARD'}
                selectedTenant={
                  selectedJobWork
                    ? selectedTenant
                    : user?.tenant_info?.tenant_id
                }
                reconciliationType={reconciliationType}
                enableAdvanceSearch
                handleMultiProductChange={handleMultiProductChange}
              />
              {!record?.isBulkUploaded && record?.product_sku_id && (
                <Fragment>
                  {record?.product_category_info?.category_path?.length
                      > 0 && (
                    <ProductCategoryLabel
                      categoryPath={
                        record?.product_category_info?.category_path
                      }
                      categoryName={record?.product_category_info?.category_path?.at(
                        -1,
                      )}
                      containerStyle={{
                        width: 'fit-content',
                      }}
                    />
                  )}
                  <textarea
                    value={record.remarks}
                    onChange={(event) => {
                      const copyData = JSON.parse(JSON.stringify(data));
                      const updatedData = copyData.map((obj) => {
                        if (obj?.key === record?.key && record?.key) {
                          return { ...obj, remarks: event.target.value };
                        }
                        return obj;
                      });
                      localDispatch({
                        type: actionTypes.SET_MULTIPLE_FIELDS,
                        payload: {
                          data: updatedData,
                        },
                      });
                    }}
                    className="invoice-table__remarks"
                    placeholder="add description here.."
                  />
                </Fragment>
              )}
            </React.Fragment>
          </div>
        );
      },
      responsive: ['sm', 'md', 'lg', 'xxl'],
    },
    {
      title: 'Available Stock',
      render: (text, record) => {
        const tenantId = selectedTenant?.tenant_id || user?.tenant_info?.tenant_id;
        const tenantProduct = record?.otherDepartmentStock?.filter((item) => item?.tenant_id === tenantId)?.[0];
        const filteredDepartmentsStock = tenantProduct?.available_stock?.filter((item) => item?.tenant_department_id !== userTenantDepartmentId);
        const totalStockInOtherDepartments = filteredDepartmentsStock?.map((dept) => Helpers.getValueTotalInObject(dept?.batches, 'available_qty')).reduce((acc, cur) => acc + cur, 0);
        const hoverContent = (
          <div>
            {filteredDepartmentsStock?.map((dept) => (
              <div key={dept.tenant_department_id} className="other-uom-stock">
                <PRZText text={dept?.department_name} />
                <PRZText
                  text={`${QUANTITY(Helpers.getValueTotalInObject(dept?.batches, 'available_qty'))} ${record?.uom_info?.uqc?.toProperCase()}`}
                  className="tenant-inventory-slider__dept-qty"
                />
              </div>
            ))}
          </div>
        );
        return (
          <React.Fragment>
            <div className={` ${(record?.isBulkUploaded && selectedAdjustmentType !== 'OUTWARD') ? 'disabled-element' : ''}`}>
              {(record?.tenant_product_id || record?.tenantProductId) && (
                <Fragment>
                  {`${QUANTITY(Helpers.getValueTotalInObject(record?.available_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'available_qty'), record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`}
                  <div
                    className="table-subscript"
                  >
                    {`In ${record?.available_batches?.filter((item) => item?.batch_in_use)?.length || '0'} Batches`}
                  </div>
                  {totalStockInOtherDepartments > 0 && (
                    <Popover
                      placement="bottom"
                      title="Stocks in other locations"
                      content={hoverContent}
                      trigger="hover"
                    >

                      <div
                        className="table-subscript"
                        style={{
                          color: 'rgb(109 107 106)',
                        }}
                      >
                        {`+${QUANTITY(totalStockInOtherDepartments, record?.uom_info?.precision)}  ${record?.uom_info?.uqc?.toProperCase() || ''} in other locations`}
                        <InfoCircleOutlined style={{
                          color: 'rgb(45, 124, 246)',
                          margin: '2px',
                        }}
                        />
                      </div>
                    </Popover>
                  )}
                </Fragment>
              )}
            </div>
            <div
              className={` ${(record?.isBulkUploaded && selectedAdjustmentType !== 'OUTWARD') ? 'disabled-element' : ''}`}
              style={{
                marginTop: '8px',
              }}
            >
              {(record?.tenant_product_id || record?.secondaryAvailableQty) && (
                <Fragment>
                  {`${(record?.secondaryAvailableQty || 'N/A')} ${record?.secondaryUomUqc?.toProperCase() || ''}`}
                  {' '}
                    (Secondary)
                </Fragment>
              )}
            </div>
          </React.Fragment>
        );
      },
      responsive: ['sm', 'md', 'lg', 'xxl'],
      hidden: true,
    },
    {
      title: 'Inwards Batch',
      width: '236px',
      render: (item) => {

        const enabledWarehouseLocations = user?.tenant_info?.inventory_config?.settings?.enable_location_based_stock;

        const availableQty = Helpers.getValueTotalInObject(item?.available_batches?.filter((batch) => batch?.batch_in_use && (allStock ? !batch?.is_rejected_batch : true)), 'available_qty');
        const showInwardsBatch = () => {
          if (selectedAdjustmentType === 'INWARDS') {
            return true;
          } if (selectedAdjustmentType === 'RECONCILIATION' && (Number(item?.newQuantity) > availableQty)) {
            return true;
          }

          return false;
        };
        return (
          <Fragment>
            {showInwardsBatch() && item?.product_sku_id ? (
              <InwardsBatch
                item={item}
                formSubmitted={formSubmitted}
                grnTableData={data}
                updateGrnTableData={(value) => localDispatch({
                  type: actionTypes.SET_MULTIPLE_FIELDS,
                  payload: {
                    data: value,
                  },
                })}
                destDepartmentId={userTenantDepartmentId}
                enabledWarehouseLocations={enabledWarehouseLocations}
                availableBatches={(value) => localDispatch({
                  type: actionTypes.SET_MULTIPLE_FIELDS,
                  payload: {
                    availableBatches: value,
                  },
                })}
                selectedLineInfo={(value) => localDispatch({
                  type: actionTypes.SET_MULTIPLE_FIELDS,
                  payload: {
                    selectedLineInfo: value,
                  },
                })}
                showLineBatches={(value) => localDispatch({
                  type: actionTypes.SET_MULTIPLE_FIELDS,
                  payload: {
                    showLineBatches: value,
                  },
                })}
                isBulkUploaded={(item?.isBulkUploaded && selectedAdjustmentType !== 'OUTWARD')}
                setIsBatchValid={(value) => localDispatch({
                  type: actionTypes.SET_MULTIPLE_FIELDS,
                  payload: {
                    isBatchValid: value,
                  },
                })}
                isBatchValidValue={isBatchValid}
                isStockAdjustment
              />

            ) : '-'}
          </Fragment>
        );
      },
      responsive: ['sm', 'md', 'lg', 'xxl'],
    },
    {
      title: 'Batch Custom Fields',
      width: '236px',
      render: (item) => {
        const availableQty = Helpers.getValueTotalInObject(item?.available_batches?.filter((batch) => batch?.batch_in_use && (allStock ? !batch?.is_rejected_batch : true)), 'available_qty');
        const showInwardsBatch = () => {
          if (selectedAdjustmentType === 'INWARDS') {
            return true;
          } if (selectedAdjustmentType === 'RECONCILIATION' && (Number(item?.newQuantity) > availableQty)) {
            return true;
          }

          return false;
        };
        return (
          <Fragment>
            {item?.selectedBatch?.custom_fields && showInwardsBatch() && item?.product_sku_id
              ? (
                <BatchCustomFields
                  cfWrapperClassName="create-grn__input-row"
                  labelClassName="create-grn__input-row__label"
                  customFields={item?.selectedBatch?.custom_fields || []}
                  formSubmitted={formSubmitted}
                  customInputChange={(value, cfId) => updateBatchCfs(value, cfId, item)}
                  isHorizontalUi
                  hideTitle
                />
              ) : '-'}
          </Fragment>
        );
      },
      responsive: ['sm', 'md', 'lg', 'xxl'],
    },
    {
      title: '',
      render: (text, record) => {
        return (
          data.length !== record.key && (
            <div className="create-indent__delete-line-button" onClick={() => { handleDelete(record.key); }}>
              <CloseOutlined />
            </div>
          )
        );
      },
      responsive: ['xs', 'sm', 'md', 'lg', 'xxl'],
      width: '40px',
      fixed: 'right',
    },
  ];

  let adjustQtyIndex = 2;
  let newOnHandIndex = 4;
  if (adjustmentBasedOn === 'AVAILABLE_STOCK') {
    adjustQtyIndex = 3;
    newOnHandIndex = 2;
  }

  columns.splice(adjustQtyIndex, 0, {
    title: 'Quantity Adjusted',
    render: (text, record) => {
      return (
        <div className={` ${(record?.isBulkUploaded && selectedAdjustmentType !== 'OUTWARD') ? 'disabled-element' : ''}`}>
          {record?.product_sku_id
            ? (
              <div>
                {/* div for actual quantity for adjustment which we are using all places for calculation */}
                <div>
                  {isSecondaryUomEnabled && <span className="uom-label">{`Primary (${record.uom_info?.uqc})`}</span>}
                  <Input
                    value={record.quantity}
                    type="Number"
                    disabled={!selectedAdjustmentType || !record?.tenantProductId || adjustmentBasedOn === 'AVAILABLE_STOCK'}
                    onWheel={(event) => event.target.blur()}
                    onChange={(event) => {
                      const copyData = JSON.parse(JSON.stringify(data));
                      const oldBatches = record?.available_batches;
                      const batches = record?.available_batches?.filter((batch) => (allStock ? !batch?.is_rejected_batch : true));
                      const availableQty = Helpers.getValueTotalInObject(batches, 'available_qty');
                      if (selectedAdjustmentType === 'OUTWARD' || (selectedAdjustmentType === 'RECONCILIATION' && (event.target.value || 0) <= availableQty)) {
                        let remainingQty = Number(event.target.value);
                        for (let i = 0; i < batches?.length; i++) {
                          batches[i].consumed_qty = 0;
                        }
                        for (let i = 0; i < batches?.length; i++) {
                          if (batches[i]?.batch_in_use && (allStock ? !batches[i]?.is_rejected_batch : true)) {
                            if (remainingQty > 0 && Number(batches[i].consumed_qty || '0') < batches?.[i]?.available_qty) {
                              batches[i].consumed_qty = Math.min(batches?.[i]?.available_qty, remainingQty);
                              remainingQty = new Decimal(remainingQty).minus(new Decimal(batches[i].consumed_qty || 0)).toNumber();
                            } else {
                              batches[i].consumed_qty = 0;
                              remainingQty -= batches[i].consumed_qty;
                            }
                          }
                        }
                      }

                      const updatedData = copyData.map((obj) => {
                        if (obj.key === record.key) {
                          return {
                            ...obj, quantity: event.target.value, available_batches: batches, oldBatches,
                          };
                        }
                        return obj;
                      });
                      localDispatch({
                        type: actionTypes.SET_MULTIPLE_FIELDS,
                        payload: {
                          data: updatedData,
                        },
                      });
                    }}
                    className="orgFormInput invoice_input_style"
                    status={formSubmitted && isLineQuantityHasError(record) ? 'error' : ''}
                  />
                  {
                    formSubmitted && isLineQuantityHasError(record) && (Math.abs(record?.secondary_uom_qty) <= 0)
                      && (
                        <div className="input-error">
                          *Entered quantity is not valid
                        </div>
                      )
                  }
                </div>
                  &nbsp;
                {/* this is used for just to show the quantity in dif uom */}
                {isSecondaryUomEnabled && (
                  <div>
                    <span className="uom-label">{`Secondary (${record.secondaryUomUqc ?? 'N/A'})`}</span>
                    <Input
                      value={record.secondary_uom_qty}
                      type="Number"
                      disabled={!selectedAdjustmentType || !record?.tenantProductId || adjustmentBasedOn === 'AVAILABLE_STOCK' || !record?.secondaryUomId}
                      onWheel={(event) => event.target.blur()}
                      onChange={(event) => {
                        const copyData = JSON.parse(JSON.stringify(data));
                        const updatedData = copyData.map((obj) => {
                          if (obj.key === record.key) {
                            return { ...obj, secondary_uom_qty: event.target.value };
                          }
                          return obj;
                        });
                        localDispatch({
                          type: actionTypes.SET_MULTIPLE_FIELDS,
                          payload: {
                            data: updatedData,
                          },
                        });
                      }}
                      className="orgFormInput invoice_input_style"
                    />
                  </div>
                )}
              </div>
            ) : ''}
        </div>
      );
    },
    responsive: ['sm', 'md', 'lg', 'xxl'],
  });

  columns.splice(newOnHandIndex, 0, {
    title: 'New On Hand Stock',
    render: (item) => {
      const newQuantity = item?.quantity && item?.uom_info?.uqc ? (
          `${(selectedAdjustmentType === 'OUTWARD')
            ? QUANTITY(Helpers.getValueTotalInObject(item?.available_batches?.filter((rec) => rec?.batch_in_use), 'available_qty') - Number(item?.quantity), item?.uom_info?.precision)
            : QUANTITY(Helpers.getValueTotalInObject(item?.available_batches?.filter((rec) => rec?.batch_in_use), 'available_qty') + Number(item?.quantity), item?.uom_info?.precision)} `

      ) : '-';

      const secondaryQuantity = item?.secondary_uom_qty && item?.uom_info?.uqc ? (
          `${(selectedAdjustmentType === 'OUTWARD')
            ? QUANTITY(Number(item?.secondaryAvailableQty || 0) - Number(item?.secondary_uom_qty), item?.uom_info?.precision)
            : QUANTITY(Number(item?.secondaryAvailableQty || 0) + Number(item?.secondary_uom_qty), item?.uom_info?.precision)} `

      ) : '-';
      return (
        <div>
          <div className={` ${(item?.isBulkUploaded && selectedAdjustmentType !== 'OUTWARD') ? 'disabled-element' : ''}`}>
            {isSecondaryUomEnabled && <span className="uom-label">{`Primary (${item.uom_info?.uqc ?? ''})`}</span>}
            <Input

              value={adjustmentBasedOn === 'CURRENT_STOCK' ? Number(newQuantity) : item.newQuantity}
              type="Number"
              disabled={!selectedAdjustmentType || !item?.tenantProductId || adjustmentBasedOn === 'CURRENT_STOCK'}
              onWheel={(event) => event.target.blur()}
              onChange={(event) => {
                const copyData = JSON.parse(JSON.stringify(data));
                const availableQty = Helpers.getValueTotalInObject(item?.available_batches?.filter((batch) => batch?.batch_in_use && (allStock ? !batch?.is_rejected_batch : true)), 'available_qty');
                const oldBatches = item?.available_batches;
                const batches = item?.available_batches?.filter((batch) => (allStock ? !batch?.is_rejected_batch : true));

                if (selectedAdjustmentType === 'OUTWARD' || (selectedAdjustmentType === 'RECONCILIATION' && (event.target.value || 0) <= availableQty)) {
                  let remainingQty = Number(availableQty - event.target.value);
                  for (let i = 0; i < batches?.length; i++) {
                    batches[i].consumed_qty = 0;
                  }
                  for (let i = 0; i < batches?.length; i++) {
                    if (batches[i]?.batch_in_use) {
                      if (remainingQty > 0 && Number(batches[i].consumed_qty || '0') < batches?.[i]?.available_qty) {
                        batches[i].consumed_qty = Math.min(batches?.[i]?.available_qty, remainingQty);
                        remainingQty -= batches[i].consumed_qty;
                      } else {
                        batches[i].consumed_qty = 0;
                        remainingQty -= batches[i].consumed_qty;
                      }
                    }
                  }
                }
                const updatedData = copyData.map((obj) => {
                  if (obj.key === item.key) {
                    return {
                      ...obj,
                      quantity: ((['RECONCILIATION', 'INWARDS']?.includes(selectedAdjustmentType) && (event.target.value || 0) > availableQty) || (['INWARDS']?.includes(selectedAdjustmentType) && adjustmentBasedOn === 'AVAILABLE_STOCK')) ? event.target.value - availableQty : availableQty - event.target.value,
                      newQuantity: event.target.value,
                      available_batches: selectedAdjustmentType === 'RECONCILIATION' && (event.target.value || 0) <= availableQty ? batches?.filter((batch) => batch?.batch_id) : batches,
                      reconciliationType: selectedAdjustmentType === 'RECONCILIATION' && (Number(event.target.value) <= availableQty) ? 'OUTWARD' : 'INWARDS',
                      oldBatches,
                    };
                  }
                  return obj;
                });
                localDispatch({
                  type: actionTypes.SET_MULTIPLE_FIELDS,
                  payload: {
                    data: updatedData,
                  },
                });
              }}
              className="orgFormInput invoice_input_style"
              status={formSubmitted && isLineQuantityHasError(item) ? 'error' : ''}
            />
            {
              selectedAdjustmentType !== 'RECONCILIATION' && formSubmitted && isLineQuantityHasError(item) && (Math.abs(item?.secondary_uom_qty) <= 0)
                && (
                  <div className="input-error">
                    *Entered quantity is not valid
                  </div>
                )
            }
            {Helpers.getValueTotalInObject(item?.available_batches?.filter((rec) => rec?.is_rejected_batch), 'available_qty') > 0
              ? (
                <PRZText
                  text={`${QUANTITY(Helpers.getValueTotalInObject(item?.available_batches?.filter((rec) => rec?.is_rejected_batch), 'available_qty'))} Rejected Stock`}
                  className="table-subscript"
                />
              ) : ''}
          </div>
            &nbsp;
          {/* this is used for just to show the quantity in dif uom */}
          {isSecondaryUomEnabled && (
            <div>
              <span className="uom-label">{`Secondary (${item.secondaryUomUqc ?? 'N/A'})`}</span>
              <Input
                value={adjustmentBasedOn === 'CURRENT_STOCK' ? Number(secondaryQuantity) : item.secondaryQuantity}
                type="Number"
                disabled={!selectedAdjustmentType || !item?.tenantProductId || adjustmentBasedOn === 'CURRENT_STOCK' || !item?.secondaryUomId}
                onWheel={(event) => event.target.blur()}
                onChange={(event) => {
                  const availableQty = item?.secondaryAvailableQty || 0;
                  const copyData = JSON.parse(JSON.stringify(data));
                  const updatedData = copyData.map((obj) => {
                    if (obj.key === item.key) {
                      return {
                        ...obj,
                        secondaryQuantity: event.target.value,
                        secondary_uom_qty: ((['RECONCILIATION', 'INWARDS']?.includes(selectedAdjustmentType) && (event.target.value || 0) > availableQty) || (['INWARDS']?.includes(selectedAdjustmentType) && adjustmentBasedOn === 'AVAILABLE_STOCK')) ? event.target.value - availableQty : availableQty - event.target.value,
                      };
                    }
                    return obj;
                  });
                  localDispatch({
                    type: actionTypes.SET_MULTIPLE_FIELDS,
                    payload: {
                      data: updatedData,
                    },
                  });
                }}
                className="orgFormInput invoice_input_style"
              />
            </div>
          )}
        </div>

      );
    },
    responsive: ['sm', 'md', 'lg', 'xxl'],
  });

  if (selectedAdjustmentType === 'INWARDS') {
    return columns?.filter((item) => !item?.hidden);
  }
  return columns;
}

const mapCustomFieldsForObject = (batchCustomFieldsData = [], importedItemLines = {}) => {
  if (!Array.isArray(batchCustomFieldsData)) return []; // Ensure input is an array

  const mappedCustomFields = batchCustomFieldsData?.map((batchField) => {
    // Find a matching field in the importedData's customFields array
    const matchingField = importedItemLines?.customFields?.find(
      (cf) => cf.id == batchField?.cfEntityId
    ) || {};

    let fieldValue = matchingField?.value || '';

    // If field type is DATE and the value is not a valid date or is a number, set it to null
    if (batchField.fieldType === 'DATE' && (Number.isNaN(Date.parse(fieldValue)) || typeof fieldValue === 'number')) {
      fieldValue = '';
    }
    // If field type is ATTACHMENT, set fieldValue to an empty array
    if (batchField.fieldType === 'ATTACHMENT') {
      fieldValue = [];
    }

    return {
      ...batchField,
      fieldValue,
    };
  });

  return mappedCustomFields || [];
};

export function builBulkUploadData({ updatedData, importedData, inventoryLocations, cfV2DocStockAdjustment, selectedAdjustmentType, userTenantDepartmentId }) {

  const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocStockAdjustment?.data?.batch_custom_fields, true);

  // Create a Map for O(1) lookups - this is the key optimization
  const updatedDataMap = new Map();
  for (const item of updatedData) {
    updatedDataMap.set(item.key, item);
  }

  const finalData = [];

  // Single loop instead of nested loops - O(n) instead of O(n²)
  for (const importedItem of importedData) {
    const updatedItem = updatedDataMap.get(importedItem.key);

    // Only process if we found a matching item
    if (updatedItem) {
      const newBatch = {};
      const finalObj = {};

      const adjustmentType = selectedAdjustmentType === 'RECONCILIATION'
        ? (importedItem?.quantity > importedItem?.available_qty ? 'INWARDS' : 'OUTWARD')
        : selectedAdjustmentType;

      const quantity = selectedAdjustmentType === 'RECONCILIATION'
        ? (importedItem?.quantity > importedItem?.available_qty
          ? (importedItem?.quantity - importedItem?.available_qty)
          : (importedItem?.available_qty - importedItem?.quantity))
        : importedItem?.quantity;

      const newQuantity = selectedAdjustmentType === 'RECONCILIATION'
        ? importedItem?.quantity
        : (selectedAdjustmentType === 'INWARDS'
          ? (importedItem?.available_qty + importedItem?.quantity)
          : (importedItem?.available_qty - importedItem?.quantity));

      // New Batch Object
      newBatch.tenant_department_id = userTenantDepartmentId;
      newBatch.tenant_product_id = updatedItem?.tenant_product?.[0]?.tenant_product_id;
      newBatch.expiry_date = (((importedItem?.expiry_date === 'Invalid Date') || !(importedItem?.expiry_date))
        ? (updatedItem?.expiry_days >= 0
          ? (toISTDate(dayjs()).add(updatedItem?.expiry_days || 0, 'day')).format('YYYY-MM-DD')
          : INFINITE_EXPIRY_DATE)
        : importedItem?.expiry_date);
      newBatch.manufacturing_date = importedItem?.manufacturing_date || null;
      newBatch.batch_inward_date = importedItem?.batch_inward_date === 'Invalid Date'
        ? dayjs().format('YYYY-MM-DD')
        : importedItem?.batch_inward_date;
      newBatch.lot_number = importedItem?.lot_number?.toString();
      newBatch.custom_batch_number = importedItem?.batch_number
        || `${updatedItem?.internal_sku_code}/${dayjs().format('DDMMYY')}/${updatedItem?.product_batch_counter}`;
      newBatch.cost_price = importedItem?.cost_price || 0;
      newBatch.mrp = importedItem?.mrp || 0;
      newBatch.selling_price = importedItem?.selling_price || 0;
      newBatch.uom_id = updatedItem?.uom_id;
      newBatch.tenant_department_id = userTenantDepartmentId;
      newBatch.manual_entry = false;
      newBatch.inventory_location_id = inventoryLocations?.inventory_location?.find((i) => i?.is_active)?.inventory_location_id;
      newBatch.inventory_location_path = inventoryLocations?.inventory_location?.find((i) => i?.is_active)?.inventory_location_path;
      newBatch.is_rejected_batch = false;
      newBatch.batch_in_use = true;
      newBatch.available_qty = 0;
      newBatch.custom_fields = mapCustomFieldsForObject(batchCustomFields, importedItem);

      // Final Object
      finalObj.key = uuidv4();
      finalObj.isBulkUploaded = true;
      finalObj.selectedBatch = newBatch;
      finalObj.expiryDateFormat = updatedItem?.expiry_date_format;
      finalObj.manufacturingDateFormat = updatedItem?.manufacturing_date_format;
      finalObj.seller_code = importedItem?.seller_code;
      finalObj.tenant_product_id = updatedItem?.tenant_product?.[0]?.tenant_product_id;
      finalObj.tenantProductId = String(updatedItem?.tenant_product?.[0]?.tenant_product_id);
      finalObj.product_sku_id = updatedItem.product_sku_id;
      finalObj.sku = updatedItem.product_sku_id;
      finalObj.product_sku_name = updatedItem?.product_sku_name;
      finalObj.secondaryUomUqc = updatedItem?.secondary_uom_info?.uqc;
      finalObj.secondaryAvailableQty = importedItem?.secondary_available_stock;
      finalObj.secondary_uom_qty = importedItem?.secondary_quantity;
      finalObj.secondaryUomId = updatedItem?.secondary_uom_id;
      finalObj.quantity = quantity;
      finalObj.batchConsumptionMethod = updatedItem?.default_outwards_method || getBatchMethod(updatedItem);
      finalObj.available_batches = adjustmentType === 'INWARDS'
        ? (updatedItem?.tenant_product[0]?.product_batches?.length
          ? [newBatch, ...(updatedItem?.tenant_product[0]?.product_batches || [])]
          : [newBatch])
        : getBatches(Helpers.batchSelectionMethod(updatedItem?.default_outwards_method || getBatchMethod(updatedItem), updatedItem?.tenant_product[0]?.product_batches, quantity), quantity);
      finalObj.uom_info = updatedItem.uom_info;
      finalObj.uom_list = updatedItem?.uom_list;
      finalObj.expiryDays = updatedItem?.expiry_days;
      finalObj.expiry_days = updatedItem?.expiry_days;
      finalObj.expiryDate = updatedItem?.expiry_days >= 0
        ? (toISTDate(dayjs()).add(updatedItem?.expiry_days || 0, 'day')).format('YYYY-MM-DD')
        : INFINITE_EXPIRY_DATE;
      finalObj.newQuantity = newQuantity;
      finalObj.availableQuantity = importedItem?.available_qty;
      finalObj.internal_sku_code = updatedItem?.internal_sku_code;
      finalObj.reconciliationType = adjustmentType;
      finalData.push(finalObj);
    }
  }

  return finalData;
}