import React, { memo, useCallback, useEffect, useMemo, useReducer } from 'react';
import './style.scss';
import { useDispatch } from 'react-redux';
import { initialState, reducer, actionTypes } from './reducer';
import { useSelector } from 'react-redux';
import { builBulkUploadData, buildIndentLines, buildPayload, isLineQuantityHasError, validateAdjustmentForm, validateBatchDetailsSimple } from './helper';
import ProductActions from '@Actions/productActions';
import TenantActions from '@Actions/tenantActions';
import CfV2Actions from '@Actions/configurations/cfV2Actions';
import CMActions from '@Actions/settings/cmActions';
import InventoryLocationActions from '@Actions/settings/inventoryLocationActions';
import Helpers from '@Apis/helpers';
import getStockAdjustmentErrorList from './stockAdjustmentErrors';
import { v4 as uuidv4 } from 'uuid';
import { notification } from 'antd';
import { Switch, useHistory } from 'react-router-dom';
import ErrorHandle from '@Components/Common/ErrorHandle';
import AdjustmentDocFields from './AdjustmentDocFields';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import AdjustmentDocLines from './AdjustmentDocLines';
import AdjustmentFormFooter from './AdjustmentFormFooter';
import PRZDrawer from '@Components/Common/UI/PRZDrawer';
import BatchesList from '@Components/Common/BatchesList';

function AdjustmentForm({
  selectedJobWork,
  selectedPRForAdjustment,
  callback,
}) {
  const dispatch = useDispatch();

  const { user, org, priceMasking } = useSelector((state) => state?.UserReducers);
  const { inventoryLocations } = useSelector((state) => state?.InventoryLocationReducers);
  const { cfV2DocStockAdjustment } = useSelector((state) => state?.CFV2Reducers);
  const { customMessageStockAdjustment } = useSelector((state) => state?.CMReducers);
  const { createIndentLoading, getSellerCodesSuccess, getProductBySkusLoading } = useSelector((state) => state?.ProductReducers);

  const createIndent = useCallback((payload, callback, withApproval) => dispatch(ProductActions.createIndent(payload, callback, withApproval)), []);
  const getTenants = useCallback((page, keyword, isVerified, orgId, limit, callback) => dispatch(TenantActions.getTenants(page, keyword, isVerified, orgId, limit, callback)), []);
  const getDocCFV2 = useCallback((payload, callback) => dispatch(CfV2Actions.getDocCFV2(payload, callback)), []);
  const getProductsSuccess = useCallback((products, forOffers) => dispatch(ProductActions.getProductsSuccess(products, forOffers)), []);
  const getCM = useCallback((orgId, entityId, entityType, page, limit, tenantId, actionType) => dispatch(CMActions.getCM(orgId, entityId, entityType, page, limit, tenantId, actionType)), []);
  const getInventoryLocations = useCallback((tenantDepartmentId, inventoryLocationPath, parentLocationId, isStorable) => dispatch(InventoryLocationActions.getInventoryLocations(tenantDepartmentId, inventoryLocationPath, parentLocationId, isStorable)), []);

  const [state, localDispatch] = useReducer(reducer, initialState);

  const history = useHistory();

  const {
    data,
    selectedAdjustmentType,
    selectedAdjustmentReason,
    userDepartmentId,
    selectedDate,
    cfStockAdjustmentDoc,
    formSubmitted,
    allStock,
    adjustmentBasedOn,
    adjustmentNumber,
    adjustmentTypes,
    userTenantDepartmentId,
    createNewBatch,
    selectedTenant,
    isBulkUpload,
    showLineBatches,
    selectedLineInfo,
    availableBatches,
    toggleRejectedBatches,
  } = state;

  const isSecondaryUomEnabled = useMemo(() => user?.tenant_info?.global_config?.settings?.enable_secondary_uom, [user]);

  const isBatchValid = useMemo(() => {
    const batchConfig = org?.organisation?.[0]?.inventory_config?.settings?.batch_management;
    return validateBatchDetailsSimple(batchConfig, data?.filter((item) => item?.reconciliationType !== 'OUTWARD'), true);
  }, [data, org]);

  useEffect(() => {
    getTenants(1, '', true, user?.tenant_info?.org_id, 100, () => {});
    getCM(user?.tenant_info?.org_id, '', 'STOCK ADJUSTMENT', null, null, null, 'CUSTOM_CHARGE');
    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName: 'STOCK_ADJUSTMENT,BATCH',
    };
    getDocCFV2(payload);
    getInventoryLocations(Helpers.getTenantDepartmentId(user), '', null, '');
    if (selectedPRForAdjustment) {
      const prLines = selectedPRForAdjustment?.purchase_requistion_lines?.map((prLine) => ({
        key: uuidv4(),
        asset: prLine?.product_sku_info?.assets?.[0]?.url || '',
        product_sku_name: prLine?.product_sku_info?.product_sku_name || prLine.product_sku_name,
        quantity: Number(Math.min(prLine?.available_qty, prLine?.quantity)),
        availableQuantity: Number(prLine?.available_qty),
        purchase_requisition_id: prLine?.purchase_requisition_id,
        available_batches: this.getBatches(prLine?.available_batches, Number(Math.min(prLine?.available_qty, prLine?.quantity))),
        unit: prLine?.uom_name,
        pr_line_id: prLine?.pr_line_id,
        tenantProductId: prLine?.tenant_product_id,
        sku: prLine?.product_sku_info?.product_sku_id,
      }));
      localDispatch({
        type: actionTypes.SET_MULTIPLE_FIELDS,
        payload: {
          selectedAdjustmentType: 'OUTWARD',
          selectedAdjustmentReason: 'INVENTORY_REVALUATION',
          data: prLines,
          userDepartmentId: Helpers.getDepartmentId(user),
          userTenantDepartmentId: Helpers.getTenantDepartmentId(user),
        },
      });
    } else {
      localDispatch({
        type: actionTypes.SET_MULTIPLE_FIELDS,
        payload: {
          userDepartmentId: Helpers.getDepartmentId(user),
          userTenantDepartmentId: Helpers.getTenantDepartmentId(user),
        },
      });
    }
  }, []);

  useEffect(() => {
    if (cfV2DocStockAdjustment?.data?.success) {
      localDispatch({
        type: actionTypes.SET_MULTIPLE_FIELDS,
        payload: {
          cfStockAdjustmentDoc: CustomFieldHelpers.getCfStructure(cfV2DocStockAdjustment?.data?.document_custom_fields, true) || [],
        },
      });
    }
  }, [cfV2DocStockAdjustment]);

  const { docLevelError, lineLevelError } = getStockAdjustmentErrorList({
    selectedAdjustmentType, selectedAdjustmentReason, userDepartmentId, selectedDate, cfStockAdjustmentDoc, data, isLineQuantityHasError: (value) => isLineQuantityHasError(value), allStock, org, priceMasking, selectedJobWork, adjustmentBasedOn, isSecondaryUomEnabled, adjustmentNumber
  });

  function handleCreateIndent(withApproval) {
    const { data, selectedDate, selectedTenant, selectedAdjustmentReason, description, selectedAdjustmentType, fileList, cfStockAdjustmentDoc, userTenantDepartmentId, isBatchValid, adjustmentBasedOn, isSecondaryUomEnabled, userDepartmentId, allStock, docSeqId, adjustmentNumber, initialAdjustmentNumber } = state;

    localDispatch({
      type: actionTypes.SET_MULTIPLE_FIELDS,
      payload: { formSubmitted: true },
    });

    const { isValid } = validateAdjustmentForm({
      selectedAdjustmentType,
      selectedAdjustmentReason,
      userDepartmentId,
      selectedDate,
      cfStockAdjustmentDoc,
      data,
      allStock,
      org,
      priceMasking,
      selectedJobWork,
      adjustmentBasedOn,
      isSecondaryUomEnabled,
      adjustmentNumber,
      initialAdjustmentNumber,
      isBatchValid,
    });

    if (!isValid) {
      notification.open({ type: 'error', message: 'Please check all mandatory attributes.', duration: 4, placement: 'top' });
      return;
    }

    const indentLines = buildIndentLines(data, adjustmentBasedOn, selectedAdjustmentType, selectedPRForAdjustment, getSellerCodesSuccess, isSecondaryUomEnabled);

    const newPayload =
      selectedAdjustmentType === 'RECONCILIATION'
        ? [
          buildPayload({ type: 'INWARDS', reason: 'RECONCILIATION', lines: indentLines.filter((l) => l.reconciliationType === 'INWARDS'), user, selectedJobWork, selectedTenant, selectedDate, description, selectedPRForAdjustment, cfStockAdjustmentDoc, fileList, userTenantDepartmentId, adjustmentNumber, initialAdjustmentNumber, docSeqId }),
          buildPayload({ type: 'OUTWARD', reason: 'RECONCILIATION', lines: indentLines.filter((l) => l.reconciliationType === 'OUTWARD'), user, selectedJobWork, selectedTenant, selectedDate, description, selectedPRForAdjustment, cfStockAdjustmentDoc, fileList, userTenantDepartmentId, adjustmentNumber, initialAdjustmentNumber, docSeqId }),
        ].filter((p) => p.adjustment_lines.length > 0)
        : [
          buildPayload({ type: selectedAdjustmentType, reason: selectedAdjustmentReason, lines: indentLines, user, selectedJobWork, selectedTenant, selectedDate, description, selectedPRForAdjustment, cfStockAdjustmentDoc, fileList, userTenantDepartmentId, adjustmentNumber, initialAdjustmentNumber, docSeqId }),
        ];

    createIndent(newPayload, () => {
      if (selectedPRForAdjustment) callback();
      else history.push('/inventory/indent');
    }, withApproval);
  };

  function handleProductChangeValue(value, key) {
    localDispatch({
      type: actionTypes.HANDLE_PRODUCT_CHANGE_VALUE,
      payload: { value, key },
    });
  }

  function handleProductChange(tenantSku, key, otherDepStocksData, isMultiMode, callback) {
    localDispatch({
      type: actionTypes.HANDLE_PRODUCT_CHANGE,
      payload: {
        tenantSku,
        key,
        otherDepStocksData,
        isMultiMode,
        callback,
        user,
        inventoryLocations,
        cfV2DocStockAdjustment,
      },
    });
  }

  function handleMultiProductChange(tenantSku, key, otherDepStocksData, callback) {
    handleProductChange(tenantSku, key, otherDepStocksData, true, callback);
  }

  function onSearchBarcodeReader(data, tenantSku, key, otherDepStocksData, isMultiMode, callback) {
    localDispatch({
      type: actionTypes.ONSEARCH_BARCODE_READER,
      payload: {
        tenantSku,
        key,
        otherDepStocksData,
        isMultiMode,
        callback,
        user,
        inventoryLocations,
        cfV2DocStockAdjustment,
        data,
      },
    });
  }

  function handleDelete(key) {
    localDispatch({
      type: actionTypes.HANDLE_DELETE,
      payload: { key },
    });
  }

  function addNewRow() {
    localDispatch({
      type: actionTypes.ADD_NEW_ROW,
    });
  }

  function customInputChange(fieldValue, cfId) {
    localDispatch({
      type: actionTypes.CUSTOM_INPUT_CHANGE,
      payload: { fieldValue, cfId },
    });
  }

  function resetData() {
    localDispatch({
      type: actionTypes.RESET_DATA,
    });
  }

  function toggleBatch(record, adjustmentRow) {
    localDispatch({
      type: actionTypes.TOGGLE_BATCH,
      payload: { record, adjustmentRow },
    });
  }

  function updateBatchCfs(fieldValue, cfId, record) {
    localDispatch({
      type: actionTypes.UPDATE_BATCH_CFS,
      payload: { fieldValue, cfId, record },
    });
  }

  function addNewBatch(batch, callback) {
    localDispatch({
      type: actionTypes.ADD_NEW_BATCH,
      payload: { batch, callback },
    });
  }

  function removeNewBatch() {
    localDispatch({
      type: actionTypes.REMOVE_NEW_BATCH,
    });
  }

  function setSelectedBatch(batch) {
    localDispatch({
      type: actionTypes.SET_SELECTED_BATCH,
      payload: { batch },
    });
  }

  function onBulkUpload(updatedData, importedData) {
    const finalData = builBulkUploadData({
      updatedData,
      importedData,
      inventoryLocations,
      cfV2DocStockAdjustment,
      selectedAdjustmentType,
      userTenantDepartmentId,
    });
    localDispatch({
      type: actionTypes.SET_MULTIPLE_FIELDS,
      payload: { data: finalData },
    });
  }

  function updateBatchValue(key, value, label, batchId) {
    localDispatch({
      type: actionTypes.UPDATE_BATCH_VALUE,
      payload: { key, value, label, batchId },
    });
  }

  const renderAdjustmentDocFields = () => {
    return (
      <AdjustmentDocFields
        localDispatch={localDispatch}
        selectedJobWork={selectedJobWork}
        state={state}
        user={user}
        selectedPRForAdjustment={selectedPRForAdjustment}
        createIndentLoading={createIndentLoading}
        adjustmentTypes={adjustmentTypes}
        customMessageStockAdjustment={customMessageStockAdjustment}
        getProductsSuccess={getProductsSuccess}
        getInventoryLocations={getInventoryLocations}
        getProductBySkusLoading={getProductBySkusLoading}
        resetData={resetData}
        customInputChange={customInputChange}
      />
    );
  };

  const renderAdjustmentDocLines = () => {
    return (
      <AdjustmentDocLines
        localDispatch={localDispatch}
        data={data}
        selectedJobWork={selectedJobWork}
        createIndentLoading={createIndentLoading}
        userTenantDepartmentId={userTenantDepartmentId}
        selectedAdjustmentType={selectedAdjustmentType}
        createNewBatch={createNewBatch}
        addNewRow={addNewRow}
        getProductBySkusLoading={getProductBySkusLoading}
        allStock={allStock}
        toggleBatch={toggleBatch}
        updateBatchValue={updateBatchValue}
        onBulkUpload={onBulkUpload}
        adjustmentBasedOn={adjustmentBasedOn}
        formSubmitted={formSubmitted}
        selectedTenant={selectedTenant}
        user={user}
        handleProductChange={handleProductChange}
        handleProductChangeValue={handleProductChangeValue}
        handleMultiProductChange={handleMultiProductChange}
        isBatchValid={isBatchValid}
        handleDelete={handleDelete}
        updateBatchCfs={updateBatchCfs}
        isSecondaryUomEnabled={isSecondaryUomEnabled}
      />
    );
  };

  const renderAdjustmentFormFooter = () => {
    return (
      <AdjustmentFormFooter
        user={user}
        createIndentLoading={createIndentLoading}
        isBulkUpload={isBulkUpload}
        selectedAdjustmentType={selectedAdjustmentType}
        data={data}
        selectedJobWork={selectedJobWork}
        userTenantDepartmentId={userTenantDepartmentId}
        barCoding={user?.tenant_info?.global_config?.sub_modules?.barcoding?.is_active}
        handleCreateIndent={handleCreateIndent}
        handleProductChange={handleProductChange}
        onSearchBarcodeReader={onSearchBarcodeReader}
      />
    );
  };

  return (
    <div className="form__wrapper create-indent__wrapper">
      {(formSubmitted && (!!docLevelError?.length || !!lineLevelError?.length)) && (
        <ErrorHandle message="Mandatory fields required" docLevelErrors={docLevelError} lineLevelErrors={lineLevelError} />
      )}
      {renderAdjustmentDocFields()}
      {renderAdjustmentDocLines()}
      {renderAdjustmentFormFooter()}
      <PRZDrawer
        open={showLineBatches}
        onClose={() => localDispatch({ type: actionTypes.SET_MULTIPLE_FIELDS, payload: { showLineBatches: false } })}
        width={400}
        destroyOnClose
        title={`${(toggleRejectedBatches
          ? availableBatches?.length
          : Number(availableBatches?.filter((obj) => (obj?.is_rejected_batch === null || obj?.is_rejected_batch === false))?.length || 0)) || '0'} Available Batches`}
      >
        <div className="batch-list__status-toggle">
          Show rejected batches

          <Switch style={{ marginLeft: '10px' }} onChange={() => localDispatch({ type: actionTypes.SET_MULTIPLE_FIELDS, payload: { toggleRejectedBatches: !toggleRejectedBatches } })} />
        </div>
        <BatchesList
          batches={toggleRejectedBatches ? availableBatches : availableBatches?.filter((obj) => (obj?.is_rejected_batch === null || obj?.is_rejected_batch === false))}
          expiryDays={selectedLineInfo?.expiryDays}
          expiryDate={selectedLineInfo?.expiryDate}
          batchUom={selectedLineInfo?.batchUom}
          selectedBatch={selectedLineInfo?.selectedBatch}
          uomList={selectedLineInfo?.uomList}
          tenantProductId={selectedLineInfo?.tenantProductId}
          setSelectedBatch={(batch) => setSelectedBatch(batch)}
          onAddNewBatch={(batch, callback) => addNewBatch(batch, callback)}
          removeNewBatch={() => removeNewBatch()}
          destDepartmentId={userTenantDepartmentId}
          nextBatchCode={selectedLineInfo?.nextBatchCode}
        />
      </PRZDrawer>
    </div>
  );
}

export default memo(AdjustmentForm);