import React, {
  useEffect, useState, Fragment, useMemo,
} from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import {
  Table, Popconfirm, Select, notification, Tooltip, Collapse,
  Avatar, Drawer, Upload, DatePicker, Button, Dropdown, Menu, Input
} from 'antd';
import {
  EditOutlined,
  FallOutlined, LoadingOutlined, RiseOutlined, UploadOutlined, CheckOutlined, EditFilled,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { v4 as uuidv4 } from 'uuid';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlugCircleXmark,
  faPowerOff,
  faPlay,
  faPause,
  faStop,
  faClock,
  faTriangleExclamation,
  faFileInvoiceDollar,
  faCartFlatbed,
  faEllipsisVertical,
  faPrint,
} from '@fortawesome/free-solid-svg-icons';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import JobWorkActions from '@Actions/production/jobWorkActions';
import H3Text from '@Uilib/h3Text';
import TemplateRouteActions from '@Actions/production/templateRouteActions';
import AnalyticsActions from '@Actions/application/analyticsActions';
import Helpers from '@Apis/helpers';
import Stopwatch from '@Components/Common/StopWatch';
import Constants, { toISTDate, QUANTITY } from '@Apis/constants';
import H3Image from '@Uilib/h3Image';
import { cdnUrl } from "@Utils/cdnHelper";
import JwQuantityUpdate from '@Components/Production/JobWorks/ViewJobWork/JwQuantityUpdate';
import SelectSeller from '@Components/Common/SelectSeller';
import POForm from '@Components/Purchase/PurchaseOrders/POForm';
import SelectWorkCenter from '@Components/Common/SelectWorkCenter';
import H3Progress from '@Components/Common/H3Progress';
import InvoiceForm from '@Components/Sales/Invoice/InvoiceForm';
import IssueRawMaterial from './IssueRawMaterial';
import { getPoLinesFromJobWorks, getInvoiceLinesFromJobWorks } from '../helpers';
import quickUpdateActions from '../../../../actions/quickUpdate/quickUpdateActions';
import CompleteProcessing from './CompleteProcessing';
// import CreateIndent from '../../../Inventory/Indent/CreateIndent';
import AdjustmentForm from '../../../Inventory/Indent/CreateIndent/index.refactored';
import ProductCategoryLabel from '@Components/Common/ProductCategoryLabel';
import HideValue from '../../../Common/RestrictedAccess/HideValue';
import PRZModal from '../../../Common/UI/PRZModal';
import PRZSelect from '../../../Common/UI/PRZSelect';
import { GetOperatorList } from '@Modules/Production';
import './style.scss';

const { Option } = Select;
const { Panel } = Collapse;

/**
 *
 */
function ViewJobWork({
  selectedTenant, updateTemplateRouteStatus, jobworkId,
  selectedJobWork, updateJobWorkV2, jobOperators, updateTemplateRouteStatusLoading,
  getJobWorkById, getJobWorkByIdLoading, callback, updateJobWorkV2Loading, MONEY, user, quickUpdateEntityWise, priceMasking, isQuickView, downloadDocument,
  getOperatorList, operatorList, getOperatorListLoading,
}) {
  const [call, setCall] = useState(false);
  const [operatorSearch, setOperatorSearch] = useState('');
  const [showQuantityUpdate, setShowQuantityUpdate] = useState(false);
  const [updateType, setUpdateType] = useState('');
  const [targetQuantity, setTargetQuantity] = useState('');
  const [targetUomInfo, setTargetUomInfo] = useState(null);
  const [attachments, setAttachments] = useState([]);
  const [attachmentLoading, setAttachmentLoading] = useState(false);
  const [showCreatePoDrawer, setShowCreatePoDrawer] = useState(false);
  const [showIssueRmDrawer, setShowIssueRmDrawer] = useState(false);
  const [showCompleteProcessing, setShowCompleteProcessing] = useState(false);
  const [currentProcessingItem, setCurrentProcessingItem] = useState('');
  const [selectedOutputMaterial, setSelectedOutputMaterial] = useState(null);
  const [processingCompletionType, setProcessingCompletionType] = useState('');
  const [rmIssueType, setRmIssueType] = useState('');
  const [showManualAdjustmentDrawer, setShowManualAdjustmentDrawer] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [manPowerValue, setManPowerValue] = useState();
  const [remarks, setRemarks] = useState();
  const [updatingRemark, setUpdatingRemark] = useState(false);
  const [showPRZModal, setShowPRZModal] = useState(false);
  const [selectedDocumentId, setSelectedDocumentId] = useState(null);
  const machineStatus = (status) => {
    if (status === 'ONLINE') return { bgColor: 'rgba(47,188,110,0.0)', color: '#2fbc6e' };
    if (status === 'BREAKDOWN') return { bgColor: 'rgba(248,119,89,0.1)', color: '#FF5630FF' };
    if (status === 'OFFLINE') return { bgColor: 'rgba(248,119,89,0.1)', color: '#FF5630FF' };
    return { bgColor: '#335aa8' };
  };

  const getStatusColor = (status) => {
    if (status === 'COMPLETED') return { color: '#22C55EFF', text: 'Completed' };
    if (status === 'DRAFT') return { color: '#b0b0b0', text: 'Draft' };
    if (status === 'IN_PROGRESS') return { color: '#2d7cf7', text: 'In Progress' };
    if (status === 'PENDING') return { color: '#dcb106', text: 'Pending' };
    if (status === 'PAUSED') return { color: '#b0b0b0', text: 'Paused' };
    if (status === 'VOID') return { color: '#d2d2d2', text: 'Void' };
    return { color: '#dcb106', text: status?.replaceAll('_', ' ')?.toProperCase() };
  };

  /**
   * @param fileListData
   */
  const handleFileChange = (fileListData) => {
    const fileList = fileListData?.fileList?.map((item) => ({
      ...item,
      url: item?.response?.response?.location || item?.url,
    }));
    const files = fileList?.map((attachment) => ({
      url: attachment?.response?.response?.location || attachment?.url,
      type: attachment.type,
      name: attachment.name,
      uid: attachment.uid,
    }));
    setAttachments(files);
    if (fileListData.file.status === 'done' || fileListData.file.status === 'removed') {
      updateJobWorkV2([{
        production_route_line_id: selectedJobWork?.production_route_line_id,
        production_route_id: selectedJobWork?.production_route_id,
        attachments: files,
      }], () => setCall(!call));
    }
  };

  function convertMinutesToTime(minutes) {
    if (Number.isNaN(minutes) || minutes < 0) {
      return 'Invalid input';
    }

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = Math.floor((minutes % 60) / 1);
    const seconds = Math.round((minutes % 1) * 60);

    const formattedHours = hours > 0 ? (hours < 10 ? `0${hours}` : hours) : '00';
    const formattedMinutes = remainingMinutes > 0 ? (remainingMinutes < 10 ? `0${remainingMinutes}` : remainingMinutes) : '00';
    const formattedSeconds = seconds > 0 ? (seconds < 10 ? `0${seconds}` : seconds) : '00';

    return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
  }

  useEffect(() => {
    getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
  }, [selectedTenant, jobworkId]);

  useEffect(() => {
    setAttachments(selectedJobWork?.attachments);
    setManPowerValue(selectedJobWork?.no_of_man_power || '');
    setRemarks(selectedJobWork?.remarks || '');
  }, [selectedJobWork]);

  const stopJobCard = () => {
    if (selectedJobWork?.processing_materials?.length > 0) { /* empty */ }
    const payload = {
      production_route_line_ids: [selectedJobWork?.production_route_line_id],
      status: 'COMPLETED',
    };
    updateTemplateRouteStatus(payload, () => {
      getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
      if (callback) {
        callback();
      }
    });
  };

  const getStopJobCardInfo = () => {
    if (selectedJobWork?.input_materials?.filter((jw) => (jw.required_input_qty - jw.total_issued_qty) > 0)?.length) {
      return {
        title: 'You haven\'t issued all the required raw materials for this job card.',
        description: 'Do you want to force close this job?',
      };
    }
    if (selectedJobWork?.output_materials?.filter((jw) => (jw.required_output_qty - jw.total_issued_qty) > 0)?.length) {
      return {
        title: 'You haven\'t produced all the required goods to be produced in this job.',
        description: 'Do you want to force close this job?',
      };
    } if (['PENDING', 'BLOCKED'].includes(selectedJobWork?.status)) {
      return {
        title: 'This job card has not been started yet.',
        description: 'Do you want to force close this job?',
      };
    }
    return {
      title: 'Are you sure you want to close this job card?',
      description: '',
    };
  };

  // let timeElapsed = selectedJobWork?.status === 'IN_PROGRESS'
  //   ? dayjs().diff(toISTDate(selectedJobWork?.started_at), 'minute')
  //   : dayjs(toISTDate(selectedJobWork?.completed_at)).diff(toISTDate(selectedJobWork?.started_at), 'minute');

  // // Ensure timeElapsed is not negative
  // timeElapsed = timeElapsed < 0 ? 0 : timeElapsed;

  const totalTimeElapsed = useMemo(() => {
    dayjs.extend(utc);
    dayjs.extend(timezone); // Ensure you have timezone if you're using timezones

    // Set current time to IST
    const currentISTTime = dayjs().tz('Asia/Kolkata'); // Use tz to get IST

    let timeElapsed;

    if (selectedJobWork?.status === 'IN_PROGRESS') {
      timeElapsed = currentISTTime.diff(toISTDate(selectedJobWork?.started_at || currentISTTime));
    } else if (selectedJobWork?.status === 'PAUSED') {
      timeElapsed = dayjs(toISTDate(selectedJobWork?.last_paused_at)).diff(toISTDate(selectedJobWork?.started_at));
    } else {
      timeElapsed = dayjs(toISTDate(selectedJobWork?.completed_at)).diff(toISTDate(selectedJobWork?.started_at));
    }

    // Ensure positive time elapsed
    timeElapsed = Math.abs(timeElapsed) / 10;

    return timeElapsed;
  }, [selectedJobWork]);

  const estimatedTime = (Number(selectedJobWork?.target_quantity) / (Number(selectedJobWork?.machine?.prod_cap_per_hour) * Number(selectedJobWork?.machine?.ideal_time_efficiency)));
  const actualTime = ((totalTimeElapsed * 10) / 3600000);
  const workOrderQty = Number(Helpers.getValueTotalInObject(selectedJobWork?.linked_pos?.filter((i) => ['SENT_FOR_APPROVAL', 'ISSUED'].includes(i?.status)), 'quantity'));
  const workOrderQtyPct = Number.parseInt((Number(Helpers.getValueTotalInObject(selectedJobWork?.linked_pos?.filter((i) => ['SENT_FOR_APPROVAL', 'ISSUED'].includes(i?.status)), 'quantity') / selectedJobWork?.pending_qty) || '0') * 100);
  const grnQty = Number(Helpers.getValueTotalInObject(selectedJobWork?.linked_grns?.filter((i) => ['SENT_FOR_APPROVAL', 'ISSUED'].includes(i?.status)), 'quantity'));
  const grnPct = Number.parseInt((Number(Helpers.getValueTotalInObject(selectedJobWork?.linked_grns?.filter((i) => ['SENT_FOR_APPROVAL', 'ISSUED'].includes(i?.status)), 'quantity') / workOrderQty) || '0') * 100);

  const getStockAdjustmentLines = () => {
    const items = [];
    selectedJobWork?.adhoc_stock_adjustments?.forEach((item) => {
      item?.ia_lines?.forEach((line) => {
        items?.push({
          ...line,
          ia_id: item?.ia_id,
          ia_number: item?.ia_number,
          status: item?.status,
        });
      });
    });
    return items;
  };

  const handleUpdate = () => {
    setIsEditing(false);
    updateJobWorkV2(
      [
        {
          production_route_line_id: selectedJobWork?.production_route_line_id,
          production_route_id: selectedJobWork?.production_route_id,
          no_of_man_power: manPowerValue === '' ? 0 : Number(manPowerValue),
        },
      ]
    );
  };

  const handleRemarkUpdate = () => {
    setUpdatingRemark(false);
    updateJobWorkV2(
      [
        {
          production_route_line_id: selectedJobWork?.production_route_line_id,
          production_route_id: selectedJobWork?.production_route_id,
          remarks: remarks,
        },
      ], () => {
        if (callback) {
          callback();
        }
      }
    );
  };

  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

  return (
    <Fragment>
      {(getJobWorkByIdLoading || updateTemplateRouteStatusLoading) ? (
        <div className="loader__container">
          <div className="loader__container-left">
            <div className="loading-header">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="loading-header-row">
                  <div className="loading-header__label loadingBlock" />
                  <div className="loading-header__label loadingBlock" />
                </div>
              ))}

            </div>
            <div className="loading-address">
              {[1, 2].map((item) => (<div key={item} className="address loadingBlock" />))}
            </div>
            <div className="loading-table loadingBlock" />
            <div className="loading-header loading-total">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="loading-header-row">
                  <div className="loading-header__label loadingBlock" />
                  <div className="loading-header__label loadingBlock" />
                </div>

              ))}
            </div>
            <div className="loading-header loading-total">
              <div className="loading-header-row">
                <div className="loading-header__label loadingBlock" />
                <div className="loading-header__label loadingBlock" />
              </div>
            </div>
          </div>
          <div className="loader__container-right">
            {[1, 2, 3].map((item) => (
              <div key={item} className="loader__container-right-row">
                <div style={{ display: 'flex' }}>
                  <div className="loader__container-right-row__icon loadingBlock" />
                  <div>
                    <div className="loader__container-right-row__head loadingBlock" />
                    <div className="loader__container-right-row__sub-head loadingBlock" />
                    <div className="loader__container-right-row__sub-head loadingBlock" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className={`view-document__wrapper jw__wrapper${!isQuickView ? 'view-document__wrapper-page' : ''}`}>
          <div className="ant-row">
            <div className="ant-col-md-24">
              <div className={`view-left__wrapper ${!isQuickView ? 'is-page-view' : ''}`}>
                <div className="view-document__title">
                  <div>
                    <div className="view-document__title-number-wrapper">
                      <H3Text text={selectedJobWork?.jw_number} className="view-invoice__title-number" />
                      <div className="view-document__title-status" style={{ backgroundColor: getStatusColor(selectedJobWork?.status).color }}>
                        {getStatusColor(selectedJobWork?.status).text?.toUpperCase()}
                      </div>
                    </div>
                    <H3Text
                      text={`#${selectedJobWork?.internal_sku_code} - ${selectedJobWork?.ref_product_code ? `${selectedJobWork?.ref_product_code} ` : ''}${selectedJobWork?.product_sku_name}`}
                      className="view-jobwork__product-name"
                    />

                  </div>
                  <div className="view-document__title-actions">
                    <div className="action-buttons">
                      <div
                        style={{
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: '40px',
                        }}
                        className="action-button"
                        onClick={() => {
                          downloadDocument({
                            url: `${Constants.DOWNLOAD_JW_V2}?prl_id=${selectedJobWork?.production_route_line_id}&tenant_id=${selectedJobWork?.tenant_id}&org_id=${selectedJobWork?.org_id}`,
                            document_type: 'JOB_WORK',
                            document_number: selectedJobWork?.jw_number,
                            key: uuidv4(),
                          });
                        }}
                      >
                        <FontAwesomeIcon icon={faPrint} style={{ fontSize: '16px' }} />
                      </div>
                    </div>
                    {selectedJobWork?.lead_time_in_min ? (
                      <div className="view-document__title-actions__lead-time">
                        <H3Text text={`Est. Time - ${convertMinutesToTime(selectedJobWork?.lead_time_in_min)}`} />
                      </div>
                    ) : ''}
                    <div className="job-work__actions__wrapper">
                      <div className="job-work__actions">
                        {['PENDING', 'DRAFT', 'COMPLETED', 'PAUSED', 'VOID'].includes(selectedJobWork?.status) && (
                          <div
                            onClick={() => {
                              if (!['IN_PROGRESS', 'DRAFT', 'COMPLETED', 'VOID', 'BLOCKED'].includes(selectedJobWork?.status)) {
                                if (['OFFLINE', 'BREAKDOWN'].includes(selectedJobWork?.machine_resource_group?.allocated_machine?.status)) {
                                  notification.open({
                                    message: `Machine ${selectedJobWork?.machine_resource_group?.allocated_machine?.resource_number} ${selectedJobWork?.machine_resource_group?.allocated_machine?.resource_name} is currently in ${selectedJobWork?.machine_resource_group?.allocated_machine?.status?.toProperCase()} status.`,
                                    description: 'Job Card cannot be started if the machine is not online',
                                    placement: 'top',
                                    duration: 6,
                                    type: 'error',
                                  });
                                } else if (selectedJobWork?.incomplete_previous_job_works?.length > 0 && Number(selectedJobWork?.pending_qty) <= 0 && selectedJobWork?.status !== 'COMPLETED') {
                                  notification.open({
                                    message: 'Job Card cannot be started as the previous Job Card is not yet completed.',
                                    description: `${selectedJobWork?.incomplete_previous_job_works[0]?.jw_number} - ${selectedJobWork?.incomplete_previous_job_works[0]?.process_name}`,
                                    placement: 'top',
                                    duration: 10,
                                    type: 'error',
                                  });
                                } else {
                                  const payload = {
                                    production_route_line_ids: [selectedJobWork?.production_route_line_id],
                                    status: 'IN_PROGRESS',
                                  };
                                  updateTemplateRouteStatus(payload, () => {
                                    getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
                                    if (callback) {
                                      callback();
                                    }
                                  });
                                }
                              }
                            }}
                            className={`job-work__action-start ${(['IN_PROGRESS', 'DRAFT', 'COMPLETED', 'VOID', 'BLOCKED'].includes(selectedJobWork?.status)) ? 'job-work__action-disabled' : ''}`}
                          >
                            <FontAwesomeIcon icon={faPlay} />
                          </div>
                        )}
                        {['IN_PROGRESS'].includes(selectedJobWork?.status) && (
                          <div
                            className={`job-work__action-pause ${(['PENDING', 'DRAFT', 'COMPLETED', 'PAUSED', 'VOID', 'BLOCKED'].includes(selectedJobWork?.status) || selectedJobWork.subcontractor_seller_id) ? 'job-work__action-disabled' : ''}`}
                            onClick={() => {
                              if (!['PENDING', 'DRAFT', 'COMPLETED', 'PAUSED', 'VOID'].includes(selectedJobWork?.status) && !selectedJobWork.subcontractor_seller_id) {
                                const payload = {
                                  production_route_line_ids: [selectedJobWork?.production_route_line_id],
                                  status: 'PAUSED',
                                };
                                updateTemplateRouteStatus(payload, () => {
                                  getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
                                  if (callback) {
                                    callback();
                                  }
                                });
                              }
                            }}
                          >
                            <FontAwesomeIcon icon={faPause} />
                          </div>
                        )}
                        <Popconfirm
                          placement="bottom"
                          title={getStopJobCardInfo().title}
                          description={getStopJobCardInfo().description}
                          onConfirm={() => stopJobCard()}
                          okText="Yes"
                          cancelText="No"
                          disabled={['PENDING', 'DRAFT', 'COMPLETED', 'VOID'].includes(selectedJobWork?.status)}
                        >
                          <div
                            className={`job-work__action-stop ${(['DRAFT', 'COMPLETED', 'VOID', 'PENDING', 'BLOCKED'].includes(selectedJobWork?.status)) ? 'job-work__action-disabled' : ''}`}
                          >
                            <FontAwesomeIcon icon={faStop} />
                          </div>
                        </Popconfirm>
                      </div>
                      <div className="job-work__actions-timer">
                        {
                          selectedJobWork?.status === 'COMPLETED' && selectedJobWork?.machine?.prod_cap_per_hour > 0 && (
                            <div style={{ fontSize: '11px', fontWeight: '600', width: '70px' }}>
                              {actualTime > estimatedTime ? (
                                <span style={{ color: '#EE404C' }}>
                                  <RiseOutlined />
                                  &nbsp;
                                  {`${(((actualTime - estimatedTime) / estimatedTime) * 100)?.toFixed(2)}%`}
                                </span>
                              ) : (
                                <span style={{ color: '#1AC05D' }}>
                                  <FallOutlined />
                                  &nbsp;
                                  {`${(((estimatedTime - actualTime) / estimatedTime) * 100)?.toFixed(2)}%`}
                                </span>
                              )}

                            </div>
                          )
                        }
                        {getJobWorkByIdLoading || !selectedJobWork?.started_at ? '__:__:__'
                          : (user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.enable_time_tracking_in_job_cards && (
                            <Stopwatch
                              isRunning={selectedJobWork?.status === 'IN_PROGRESS' && dayjs(toISTDate(selectedJobWork?.started_at)).isBefore(dayjs())}
                              timeElapsed={totalTimeElapsed || 0}
                            />
                          ))}
                      </div>
                      <div>
                        <Dropdown
                          overlay={(
                            <Menu>
                              <Menu.Item key="1" onClick={() => setShowManualAdjustmentDrawer(true)}>+ Manual Adjustment</Menu.Item>
                            </Menu>
                          )}
                          trigger={['click']}
                          getPopupContainer={(triggerNode) => triggerNode.parentNode}
                        >
                          <div className="view-jw__dropdown">
                            <FontAwesomeIcon icon={faEllipsisVertical} />
                          </div>
                        </Dropdown>
                      </div>
                    </div>
                  </div>
                  {
                    !selectedJobWork?.mo_adjustment_id && ['PENDING', 'IN_PROGRESS'].includes(selectedJobWork?.status) && (
                      <div className="action-buttons" style={{ marginLeft: 'auto !important' }}>
                        <Popconfirm
                          placement="topRight"
                          title={`Are you sure you want to ${selectedJobWork?.status !== 'COMPLETED' ? 'complete' : 'start'} this Job Card?`}
                          onConfirm={() => {
                            const payload = {
                              production_route_line_ids: [selectedJobWork?.production_route_line_id],
                              status: selectedJobWork?.status !== 'COMPLETED' ? 'COMPLETED' : 'IN_PROGRESS',
                            };
                            updateTemplateRouteStatus(payload, () => {
                              getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
                              if (callback) {
                                callback();
                              }
                            });
                          }}
                          okText="Yes"
                          cancelText="No"
                        >
                          <div className="action-button action-button-big">
                            {(updateTemplateRouteStatusLoading)
                              ? (
                                <div style={{ marginTop: '-5px' }}>
                                  <LoadingOutlined />
                                </div>
                              ) : (
                                <Fragment>{selectedJobWork?.status === 'IN_PROGRESS' ? 'Start' : 'Mark as Completed'}</Fragment>
                              )}
                          </div>
                        </Popconfirm>
                      </div>
                    )
                  }
                </div>
                <div className="document-header">
                  <div className="ant-row">
                    <div className="ant-col-md-24">
                      <H3Text
                        text={selectedJobWork?.process_name}
                        className="document-header__party-name"
                      />
                    </div>
                    <div className="ant-col-md-12">
                      <div className="ant-row">
                        <div className="ant-col-md-24">
                          <div className="document-header__field">
                            <H3Text text="Order#" className="document-header__field-name" />
                            <H3Text
                              text={(
                                <Link to={`/production/manufacturing-orders/view/${selectedJobWork?.mo_id}`} target="_blank">
                                  {selectedJobWork?.mo_number}
                                </Link>
                              )}
                              className="document-header__field-value"
                            />
                          </div>
                        </div>
                        {selectedJobWork?.mo_adjustment_id && (
                          <div className="ant-col-md-24">
                            <div className="document-header__field">
                              <H3Text text="Batch" className="document-header__field-name" />
                              <H3Text
                                text={`#${selectedJobWork?.fg_adjustment?.custom_batch_number}`}
                                className="document-header__field-value"
                              />
                            </div>
                          </div>
                        )}
                        <div className="ant-col-md-24">
                          <div className="document-header__field">
                            <H3Text text="Order Status" className="document-header__field-name" />
                            <H3Text
                              text={(
                                <div
                                  className="status-tag"
                                  style={{ backgroundColor: Helpers.getStatusColor(selectedJobWork?.mo_status).color }}
                                >
                                  {Helpers.getStatusColor(selectedJobWork?.mo_status).text}
                                </div>
                              )}
                              className="document-header__field-value"
                            />
                          </div>
                        </div>
                        <div className="ant-col-md-24">
                          <Tooltip title={selectedJobWork?.tenant_info?.tenant_name}>
                            <div className="document-header__field">
                              <H3Text text="Location" className="document-header__field-name" />
                              <H3Text
                                text={selectedJobWork?.tenant_info?.tenant_name?.length > 25 ? `${selectedJobWork?.tenant_info?.tenant_name?.substring(0, 25)}..` : selectedJobWork?.tenant_info?.tenant_name}
                                className="document-header__field-value"
                              />
                            </div>
                          </Tooltip>
                        </div>
                        <div className="ant-col-md-24">
                          <div className="document-header__field">
                            <H3Text text="Department" className="document-header__field-name" />
                            <H3Text
                              text={selectedJobWork?.tenant_department_info?.department_name}
                              className="document-header__field-value"
                            />
                          </div>
                        </div>
                        {!selectedJobWork?.seller_info
                          && (
                            <div className="ant-col-md-24">
                              <div className="document-header__field">
                                <H3Text text="Work Center" className="document-header__field-name" />
                                <SelectWorkCenter
                                  selectedWorkCenterId={selectedJobWork?.machine_resource_group?.resource_group_id}
                                  onChange={(value) => {
                                    updateJobWorkV2([
                                      {
                                        production_route_line_id: selectedJobWork?.production_route_line_id,
                                        production_route_id: selectedJobWork?.production_route_id,
                                        resources: value ? [
                                          {
                                            resource_group_id: value,
                                            resource_ids: [],
                                          },
                                        ] : [],
                                        assignees: selectedJobWork?.assignees?.map((assignee) => (assignee?.user_id)),
                                      },
                                    ], () => {
                                      getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
                                      if (callback) {
                                        callback();
                                      }
                                    });
                                  }}
                                  dropdownWidth="180px"
                                  disabled={selectedJobWork?.seller_info}
                                  height="auto"
                                  containerClassName="document-header__field-value"
                                />
                              </div>
                            </div>
                          )}
                        <div className="ant-col-md-24">
                          <div className="document-header__field">
                            <H3Text text="Quantity" className="document-header__field-name" />
                            <H3Text
                              text={`${selectedJobWork?.target_quantity} ${selectedJobWork?.fg_uom_info?.uqc ? selectedJobWork?.fg_uom_info?.uqc?.toProperCase() : selectedJobWork?.sfg_uom_info?.uqc?.toProperCase()}`}
                              className="document-header__field-value"
                            />
                          </div>
                        </div>
                        <div className="ant-col-md-24">
                          <div className="document-header__field">
                            <H3Text text="Manpower Used" className="document-header__field-name" />
                            <H3Text
                              text={(
                                <div>
                                  {isEditing ? (
                                    <div>
                                      <Input
                                        type="number"
                                        style={{
                                          height: '28px',
                                          width: '180px',
                                          fontSize: '12px',
                                        }}
                                        value={manPowerValue}
                                        onChange={(e) => setManPowerValue(e.target.value)}
                                        onKeyPress={(e) => {
                                          if (e.key === 'Enter') handleUpdate();
                                        }}
                                        disabled={['COMPLETED', 'VOID'].includes(selectedJobWork?.status) || updateJobWorkV2Loading}
                                      />
                                      <CheckOutlined
                                        style={{ color: '#1890ff', cursor: 'pointer', marginLeft: '8px' }}
                                        onClick={handleUpdate}
                                      />
                                    </div>
                                  ) : (
                                    <div className="document-header__field-value">
                                      {manPowerValue || ''}
                                      {!['COMPLETED', 'VOID'].includes(selectedJobWork?.status) && (
                                        <EditFilled
                                          className="edit-icon"
                                          style={{ marginLeft: '8px', cursor: 'pointer' }}
                                          onClick={() => setIsEditing(true)}
                                        />
                                      )}
                                    </div>
                                  )}
                                </div>
                              )}
                              className="document-header__field-value"
                            />
                          </div>
                        </div>
                        {!selectedJobWork?.modified_by_info && (
                          <div className="ant-col-md-24">
                            <Tooltip title={`Updated By ${selectedJobWork?.modified_by_info?.first_name}`}>
                              <div className="document-header__field">
                                <H3Text text="Completed At" className="document-header__field-name" />
                                <H3Text
                                  text={dayjs(selectedJobWork?.modified_at).format('DD MMM, YY hh:mm A')}
                                  className="document-header__field-value"
                                />
                              </div>
                            </Tooltip>
                          </div>
                        )}
                        {selectedJobWork?.completed_at && (
                          <div className="ant-col-md-24">
                            <Tooltip title={`Completed By ${selectedJobWork?.modified_by_info?.first_name}`}>
                              <div className="document-header__field">
                                <H3Text text="Completed At" className="document-header__field-name" />
                                <H3Text
                                  text={dayjs(selectedJobWork?.completed_at).format('DD MMM, YY hh:mm A')}
                                  className="document-header__field-value"
                                />
                              </div>
                            </Tooltip>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="ant-col-md-12">
                      <div className="ant-row">
                        {
                          // selectedJobWork?.machine_resource_group?.resource_group_id &&
                          (
                            <div className="ant-col-md-24">
                              <div className="document-header__field">
                                <H3Text text="Machine" className="document-header__field-name" />
                                <H3Text
                                  text={(
                                    <PRZSelect
                                      value={selectedJobWork?.machine_resource_group?.allocated_machine?.resource_id}
                                      filterOption={false}
                                      onChange={(value) => {
                                        updateJobWorkV2([
                                          {
                                            production_route_line_id: selectedJobWork?.production_route_line_id,
                                            production_route_id: selectedJobWork?.production_route_id,
                                            resources: [
                                              {
                                                resource_group_id: selectedJobWork?.machine_resource_group?.resource_group_id,
                                                resource_ids: value ? [
                                                  value,
                                                ] : [],
                                              },
                                            ],
                                            assignees: selectedJobWork?.assignees?.map((assignee) => (assignee?.user_id)),
                                          },
                                        ], () => {
                                          getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
                                          if (callback) {
                                            callback();
                                          }
                                        });
                                      }}
                                      style={{
                                        width: '180px',
                                      }}
                                      showSearch
                                      disabled={['COMPLETED', 'VOID'].includes(selectedJobWork?.status)}
                                      allowClear
                                      placeholder=""
                                    >
                                      {selectedJobWork?.machine_resource_group?.all_machines?.map((item) => (
                                        <Option key={item.resource_id} value={item.resource_id}>
                                          <span
                                            className="resource-status__icon"
                                            style={{
                                              color: (['ONLINE'].includes(item?.status) && item?.occupancy_status === 'IN_USE') ? '#EE404C' : machineStatus(item?.status)?.color,
                                              marginRight: '5px',
                                            }}
                                          >
                                            {['OFFLINE'].includes(item?.status)
                                              && <FontAwesomeIcon icon={faPowerOff} />}
                                            <Tooltip title="This machine is Online and available for use">
                                              {['ONLINE'].includes(item?.status) && item?.occupancy_status === 'FREE'
                                                && <FontAwesomeIcon icon={faPowerOff} />}
                                            </Tooltip>
                                            <Tooltip title="This machine currently in use">
                                              {['ONLINE'].includes(item?.status) && item?.occupancy_status === 'IN_USE'
                                                && <FontAwesomeIcon icon={faClock} />}
                                            </Tooltip>
                                            {['BREAKDOWN'].includes(item?.status) && <FontAwesomeIcon icon={faPlugCircleXmark} />}
                                          </span>
                                          {`#${item?.resource_number} - ${item?.resource_name}`}
                                        </Option>
                                      ))}
                                    </PRZSelect>
                                  )}
                                  className="document-header__field-value"
                                />
                                <Tooltip title="Please select work center before selecting machine." style={{ fontSize: '10px' }}>
                                  <InfoCircleOutlined
                                    style={{ color: '#2d7df7', cursor: 'pointer', marginLeft: '2px' }}
                                  />
                                </Tooltip>
                              </div>
                            </div>
                          )}
                        <div className="ant-col-md-24">
                          <div className="document-header__field">
                            <H3Text text="Operator" className="document-header__field-name" />
                            <H3Text
                              text={(
                                <PRZSelect
                                  value={selectedJobWork?.assignees?.[0]?.operator_id}
                                  onChange={(operatorId) => {
                                    updateJobWorkV2([
                                      {
                                        production_route_line_id: selectedJobWork?.production_route_line_id,
                                        production_route_id: selectedJobWork?.production_route_id,
                                        resources: selectedJobWork?.machine_resource_group?.resource_group_id ? [
                                          {
                                            resource_group_id: selectedJobWork?.machine_resource_group?.resource_group_id,
                                            resource_ids: selectedJobWork?.machine_resource_group?.allocated_machine?.resource_id ? [selectedJobWork?.machine_resource_group?.allocated_machine?.resource_id] : [],
                                          },
                                        ] : [],
                                        assignees: [operatorId],
                                      },
                                    ], () => {
                                      getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
                                      if (callback) {
                                        callback();
                                      }
                                    });
                                  }}
                                  style={{
                                    width: '180px',
                                  }}
                                  filterOption={false}
                                  showSearch
                                  onSearch={(val) => setOperatorSearch(val)}
                                  disabled={['COMPLETED', 'VOID'].includes(selectedJobWork?.status)}
                                  placeholder=""
                                >
                                  {
                                    operatorList?.filter((item) => item?.tenant_id === selectedJobWork?.tenant_info?.tenant_id)
                                      ?.filter((item) => item?.first_name?.toLowerCase()?.includes(operatorSearch?.toLowerCase()))
                                      ?.map((item) => (
                                        <Option value={item?.operator_id} key={item?.operator_id}>
                                          <Avatar
                                            style={{
                                              backgroundColor: Helpers.getAvatarColors(item?.first_name?.trim().toUpperCase()?.[0])?.color1,
                                              width: '16px',
                                              height: '16px',
                                              marginTop: '-2px',
                                              lineHeight: '16px',
                                            }}
                                          >
                                            {item?.first_name?.trim().slice(0, 2).toUpperCase()}
                                          </Avatar>
                                          &nbsp;
                                          {item?.first_name.trim()}
                                        </Option>
                                      ))
                                  }
                                </PRZSelect>
                                // <PRZSelect
                                //   value={selectedJobWork?.assignees?.[0]?.user_id}
                                //   onChange={(userId) => {
                                //     updateJobWorkV2([
                                //       {
                                //         production_route_line_id: selectedJobWork?.production_route_line_id,
                                //         production_route_id: selectedJobWork?.production_route_id,
                                //         resources: selectedJobWork?.machine_resource_group?.resource_group_id ? [
                                //           {
                                //             resource_group_id: selectedJobWork?.machine_resource_group?.resource_group_id,
                                //             resource_ids: selectedJobWork?.machine_resource_group?.allocated_machine?.resource_id ? [selectedJobWork?.machine_resource_group?.allocated_machine?.resource_id] : [],
                                //           },
                                //         ] : [],
                                //         assignees: [userId],
                                //       },
                                //     ], () => {
                                //       getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
                                //       if (callback) {
                                //         callback();
                                //       }
                                //     });
                                //   }}
                                //   style={{
                                //     width: '180px',
                                //   }}
                                //   filterOption={false}
                                //   showSearch
                                //   onSearch={(val) => setOperatorSearch(val)}
                                //   disabled={['COMPLETED', 'VOID'].includes(selectedJobWork?.status)}
                                //   placeholder=""
                                // >
                                //   {
                                //     jobOperators?.find((item) => item?.tenant_id === selectedJobWork?.tenant_info?.tenant_id)?.users
                                //       ?.filter((item) => item?.first_name?.toLowerCase()?.includes(operatorSearch?.toLowerCase()))
                                //       ?.map((item) => (
                                //         <Option value={item?.user_id} key={item?.user_id}>
                                //           <Avatar
                                //             style={{
                                //               backgroundColor: Helpers.getAvatarColors(item?.identity?.[0])?.color1,
                                //               width: '16px',
                                //               height: '16px',
                                //               marginTop: '-2px',
                                //               lineHeight: '16px',
                                //             }}
                                //           >
                                //             {item?.identity}
                                //           </Avatar>
                                //           &nbsp;
                                //           {item?.first_name}
                                //         </Option>
                                //       ))
                                //   }
                                // </PRZSelect>
                              )}
                              className="document-header__field-value"
                            />
                          </div>
                        </div>
                        {!selectedJobWork?.machine_resource_group?.resource_group_id && (
                          <div className="ant-col-md-24">
                            <div className="document-header__field">
                              <H3Text text="Subcontractor" className="document-header__field-name" />
                              <H3Text
                                text={(
                                  <div className="seller-selector-jb">
                                    <SelectSeller
                                      onChange={(value) => {
                                        updateJobWorkV2([
                                          {
                                            production_route_line_id: selectedJobWork?.production_route_line_id,
                                            production_route_id: selectedJobWork?.production_route_id,
                                            subcontractor_seller_id: value?.seller_id || null,
                                          },
                                        ], () => {
                                          getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
                                          if (callback) {
                                            callback();
                                          }
                                        });
                                      }}
                                      selectedSeller={selectedJobWork?.subcontractor_seller_id}
                                      tenantId={selectedJobWork?.tenant_id}
                                      notRequired
                                      isSubcontractor
                                      hideTitle
                                      inputClassName="IGNORE"
                                      placeholder="Select subcontractor"
                                      mapSellerId
                                      disabled={['VOID', 'COMPLETED'].includes(selectedJobWork?.status)}
                                    />
                                  </div>
                                )}
                                className="document-header__field-value"
                              />
                            </div>
                          </div>
                        )}
                        {(user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.enable_time_tracking_in_job_cards && selectedJobWork?.status != 'PENDING') && (
                          <React.Fragment>
                            {(
                              <div className="ant-col-md-24">
                                <div className="document-header__field">
                                  <H3Text text="Start Time" className="document-header__field-name" />
                                  <H3Text
                                    text={(
                                      <div className="date-selector-jb">
                                        <DatePicker
                                          value={selectedJobWork?.started_at ? toISTDate(selectedJobWork.started_at) : null}
                                          onChange={(value) => quickUpdateEntityWise({
                                            production_route_line_id: selectedJobWork?.production_route_line_id,
                                            started_at: value,
                                            org_id: user?.tenant_info?.org_id,
                                            entity_name: 'JOB_CARDS',
                                          }, () => {
                                            getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
                                            if (callback) {
                                              callback();
                                            }
                                          })}
                                          format="DD-MM-YYYY hh:mm A"
                                          showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
                                          disabledDate={(current) => current && current > dayjs().endOf('day')}
                                          disabled={!user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.allow_user_to_update_start_time_and_end_time}
                                        />
                                      </div>
                                    )}
                                    className="document-header__field-value"
                                  />
                                </div>
                              </div>
                            )}
                            {selectedJobWork?.status == 'COMPLETED' && user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.enable_time_tracking_in_job_cards && (
                              <div className="ant-col-md-24">
                                <div className="document-header__field">
                                  <H3Text text="Completion Time" className="document-header__field-name" />
                                  <H3Text
                                    text={(
                                      <DatePicker
                                        value={selectedJobWork?.completed_at ? toISTDate(selectedJobWork.completed_at) : null}
                                        onChange={(value) => quickUpdateEntityWise({
                                          production_route_line_id: selectedJobWork?.production_route_line_id,
                                          completed_at: value,
                                          org_id: user?.tenant_info?.org_id,
                                          entity_name: 'JOB_CARDS',
                                        }, () => {
                                          getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
                                          if (callback) {
                                            callback();
                                          }
                                        })}
                                        format="DD-MM-YYYY hh:mm A"
                                        showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
                                        disabledDate={(current) => current && current > dayjs().endOf('day')}
                                        disabled={selectedJobWork?.status != 'COMPLETED' || !user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.allow_user_to_update_start_time_and_end_time}
                                      />
                                    )}
                                    className="document-header__field-value"
                                  />
                                </div>
                              </div>
                            )}
                          </React.Fragment>
                        )}

                        <div className="ant-col-md-24">
                          <div className="document-header__field">
                            <H3Text text="Attachments" className="document-header__field-name" />
                            <Upload
                              action={Constants.UPLOAD_FILE}
                              fileList={attachments}
                              onChange={(upload) => {
                                if (upload?.file?.status === 'done' || upload?.file?.status === 'removed') {
                                  setAttachmentLoading(false);
                                } else {
                                  setAttachmentLoading(true);
                                }
                                handleFileChange(upload);
                              }}
                              className="document-header__field-value"
                              disabled={updateJobWorkV2Loading || attachmentLoading || ['COMPLETED', 'VOID'].includes(selectedJobWork?.status)}
                            >
                              {attachments?.length >= 5 ? null : ((updateJobWorkV2Loading || attachmentLoading) ? <LoadingOutlined style={{ fontSize: '35px' }} /> : (
                                <Button
                                  type="dashed"
                                  bordered={false}
                                  icon={<UploadOutlined />}
                                  size="small"
                                >
                                  Upload
                                </Button>
                              ))}
                            </Upload>
                          </div>
                        </div>
                        {user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.enable_time_tracking_in_job_cards && (<div className="ant-col-md-24">
                          <div className="document-header__field">
                            <H3Text text="Total Pause Time" className="document-header__field-name" />
                            <H3Text
                              text={convertMinutesToTime(selectedJobWork?.total_pause_time_in_min)}
                              className="document-header__field-value"
                            />
                          </div>
                        </div>)}
                        <div className="ant-col-md-24">
                          <div className="document-header__field">
                            <H3Text text="Remarks" className="document-header__field-name" />
                            <div className="document-header__field-value" style={{ display: 'flex', }}>
                              <textarea
                                rows="3"
                                onChange={(event) => {
                                  setRemarks(event.target.value);
                                }}
                                value={remarks}
                                style={{
                                  resize: 'none',
                                  backgroundColor: 'white',
                                  border: updatingRemark ? '1px solid #1890ff' : 'none',
                                  borderRadius: '4px',
                                  padding: '4px 8px',
                                  width: '180px',
                                }}
                                placeholder='No remarks...'
                                disabled={!updatingRemark}
                              />
                              {updatingRemark ? (
                                <CheckOutlined
                                  style={{ color: '#1890ff', cursor: 'pointer', marginLeft: '8px', }}
                                  onClick={() => handleRemarkUpdate()}
                                />
                              ) : (
                                !['COMPLETED', 'VOID'].includes(selectedJobWork?.status) && (
                                  <EditFilled
                                    className="edit-icon"
                                    style={{ cursor: 'pointer', marginLeft: '8px', }}
                                    onClick={() => setUpdatingRemark(true)}
                                  />
                                )
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="view-jobwork__materials">
                  {
                    selectedJobWork?.output_materials?.length > 0 && (
                      <div className="mg-top-10">
                        <Table
                          pagination={false}
                          title={() => (
                            <div className="flex-display alex-align-c">
                              Goods to Be Produced
                              <div className="flex-display alex-align-c margin-left-auto">
                                {
                                  selectedJobWork?.subcontractor_seller_id ? (
                                    <Button
                                      type="primary"
                                      onClick={() => {
                                        setShowCreatePoDrawer(true);
                                        setRmIssueType('RAW_MATERIAL');
                                      }}
                                      disabled={!selectedJobWork?.subcontractor_seller_id || ['COMPLETED', 'VOID']?.includes(selectedJobWork?.status)}
                                      size="small"
                                    >
                                      + Work Order
                                    </Button>
                                  ) : (
                                    <Button
                                      type="primary"
                                      onClick={() => {
                                        const record = selectedJobWork?.output_materials?.[0];
                                        setTargetQuantity(selectedJobWork?.pending_qty ?? 0);
                                        setTargetUomInfo(selectedJobWork?.fg_uom_info || selectedJobWork?.sfg_uom_info);
                                        setShowQuantityUpdate(true);
                                        setUpdateType('PENDING');
                                        setSelectedOutputMaterial(record);
                                      }}
                                      disabled={['PENDING', 'BLOCKED', 'VOID']?.includes(selectedJobWork?.status) || selectedJobWork?.subcontractor_seller_id}
                                      size="small"
                                    >
                                      + Issue Stock
                                    </Button>
                                  )
                                }
                              </div>
                            </div>
                          )}
                          size="small"
                          columns={[
                            {
                              title: 'SKU',
                              render: (record) => (
                                <Fragment>
                                  <div onClick={() => { setShowPRZModal(true); setSelectedDocumentId(record?.product_sku_id); }}>
                                    <a>
                                      {`#${record?.internal_sku_code}`}
                                    </a>
                                  </div>
                                  {` ${record?.ref_product_code ? `${record?.ref_product_code} - ` : ''} ${record?.product_sku_name}`}
                                  {record?.product_category_info?.category_path?.length > 0 && (
                                    <ProductCategoryLabel
                                      categoryPath={record?.product_category_info?.category_path}
                                      categoryName={record?.product_category_info?.category_path?.at(-1)}
                                      containerStyle={{
                                        width: 'fit-content',
                                      }}
                                    />
                                  )}
                                </Fragment>
                              ),
                            },
                            {
                              title: 'Target',
                              render: (record) => `${QUANTITY(record?.required_output_qty ?? 0, record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`,
                            },
                            {
                              title: 'Pending',
                              render: (record) => {
                                return (
                                  <div className="flex-display flex-align-c jw-wrapper__material-qty">
                                    {`${QUANTITY(selectedJobWork?.pending_qty, record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`}
                                  </div>
                                );
                              },
                            },
                            {
                              title: 'Rework',
                              render: (record) => (
                                <div className="flex-display flex-align-c jw-wrapper__material-qty">
                                  {`${QUANTITY(selectedJobWork?.rework_qty ?? 0, record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`}
                                  &nbsp;
                                  {['PENDING', 'IN_PROGRESS', 'BLOCKED', 'VOID']?.includes(selectedJobWork?.status) && !selectedJobWork?.subcontractor_seller_id && Number(selectedJobWork?.rework_qty ?? 0) > 0 && (
                                    <div onClick={() => {
                                      setTargetQuantity(selectedJobWork?.rework_qty);
                                      setTargetUomInfo(selectedJobWork?.fg_uom_info || selectedJobWork?.sfg_uom_info);
                                      setShowQuantityUpdate(true);
                                      setUpdateType('REWORK');
                                      setSelectedOutputMaterial(record);
                                    }}
                                    >
                                      <EditOutlined />
                                    </div>
                                  )}
                                </div>
                              ),
                            },
                            {
                              title: 'Rejected',
                              render: (record) => {
                                return (
                                  <div className="flex-display flex-align-c jw-wrapper__material-qty">
                                    {`${QUANTITY(selectedJobWork?.rejected_qty ?? 0, record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`}
                                  </div>
                                );
                              },
                            },
                            {
                              title: 'Completed',
                              render: (record) => (
                                <div>
                                  {`${QUANTITY(Number(record?.total_issued_qty || Number(record?.total_grn_qty_for_processing) || 0), record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`}
                                  <H3Progress percent={Number.parseInt((Number(record?.total_issued_qty || Number(record?.total_grn_qty_for_processing) || 0)) / (record?.required_output_qty) * 100)} barWidth="70px" />
                                </div>
                              ),
                            },
                          ]}
                          dataSource={selectedJobWork?.output_materials}
                          bordered
                        />
                      </div>
                    )
                  }
                  {
                    selectedJobWork?.input_materials?.length > 0 && (
                      <div className="mg-top-10">
                        <div className="view-jw-table__wrapper">
                          <Table
                            pagination={false}
                            title={() => (
                              <div className="flex-display alex-align-c">
                                Required Raw Materials
                                <div className="flex-display alex-align-c margin-left-auto">
                                  <Button
                                    type="primary"
                                    onClick={() => {
                                      setShowIssueRmDrawer(true);
                                      setRmIssueType('RAW_MATERIAL');
                                    }}
                                    size="small"
                                    disabled={['COMPLETED', 'BLOCKED', 'VOID']?.includes(selectedJobWork?.status)}
                                  >
                                    + Issue Stock
                                  </Button>
                                </div>
                              </div>
                            )}
                            size="small"
                            columns={[
                              {
                                title: 'SKU',
                                render: (record) => (
                                  <Fragment>
                                    <div onClick={() => { setShowPRZModal(true); setSelectedDocumentId(record?.product_sku_id); }}>
                                      <a>
                                        {`#${record?.internal_sku_code}`}
                                      </a>
                                    </div>
                                    {` ${record?.ref_product_code ? `${record?.ref_product_code} - ` : ''} ${record?.product_sku_name}`}
                                    {record?.product_category_info?.category_path?.length > 0 && (
                                      <ProductCategoryLabel
                                        categoryPath={record?.product_category_info?.category_path}
                                        categoryName={record?.product_category_info?.category_path?.at(-1)}
                                        containerStyle={{
                                          width: 'fit-content',
                                        }}
                                      />
                                    )}
                                  </Fragment>
                                ),
                              },
                              {
                                title: 'Available Stock',
                                render: (record) => `${QUANTITY(Helpers.getValueTotalInObject(record?.product_batches?.filter((item) => item?.is_usable), 'available_qty'), record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`,
                              },
                              {
                                title: 'Required Quantity',
                                render: (record) => `${QUANTITY(record?.required_input_qty ?? 0, record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`,
                              },
                              {
                                title: 'Issued Quantity',
                                render: (record) => (
                                  <Fragment>
                                    {`${Number(record?.total_issued_qty || 0)} ${record?.uom_info?.uqc?.toProperCase()}`}
                                    <H3Progress percent={Number.parseInt((Number(record?.total_issued_qty || 0)) / (record?.required_input_qty) * 100)} barWidth="70px" />
                                  </Fragment>
                                ),
                              },
                            ]}
                            dataSource={selectedJobWork?.input_materials}
                            bordered
                          />
                        </div>
                      </div>
                    )
                  }
                  {
                    selectedJobWork?.processing_materials?.length > 0 && (
                      <div className="mg-top-10">
                        <Table
                          pagination={false}
                          title={() => (
                            <div className="flex-display alex-align-c">
                              Goods to Be Processed
                              <div className="flex-display alex-align-c margin-left-auto">
                                <Button
                                  type="primary"
                                  onClick={() => {
                                    setShowIssueRmDrawer(true);
                                    setRmIssueType('PROCESSING_MATERIAL');
                                  }}
                                  size="small"
                                  disabled={['COMPLETED', 'BLOCKED', 'VOID']?.includes(selectedJobWork?.status)}
                                >
                                  + Issue Stock
                                </Button>
                                {selectedJobWork?.subcontractor_seller_id && (
                                  <Button
                                    type="primary"
                                    onClick={() => {
                                      setShowCreatePoDrawer(true);
                                      setRmIssueType('PROCESSING_MATERIAL');
                                      // TODO: add logic for creating work order
                                    }}
                                    disabled={!selectedJobWork?.subcontractor_seller_id}
                                    size="small"
                                    style={{ marginLeft: '5px' }}
                                  >
                                    + Work Order
                                  </Button>
                                )}
                              </div>
                            </div>
                          )}
                          size="small"
                          columns={[
                            {
                              title: 'Product',
                              render: (record) => (
                                <Fragment>
                                  <Link to={`/inventory/product/view/${record.product_sku_id}`} target="_blank">
                                    {`#${record?.internal_sku_code}`}
                                  </Link>
                                  {` ${record?.ref_product_code ? `${record?.ref_product_code} - ` : ''} ${record?.product_sku_name}`}
                                  {record?.product_category_info?.category_path?.length > 0 && (
                                    <ProductCategoryLabel
                                      categoryPath={record?.product_category_info?.category_path}
                                      categoryName={record?.product_category_info?.category_path?.at(-1)}
                                      containerStyle={{
                                        width: 'fit-content',
                                      }}
                                    />
                                  )}
                                </Fragment>
                              ),
                            },
                            {
                              title: 'Total Required',
                              render: (record) => (
                                <div className="flex-display flex-align-c jw-wrapper__material-qty">
                                  {`${QUANTITY(Number(record?.required_processing_qty || 0), record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`}
                                </div>
                              ),
                            },
                            {
                              title: 'Work in Progress',
                              render: (record) => (
                                <div className="flex-display flex-align-c jw-wrapper__material-qty">
                                  {(() => {
                                    const pendingQty =
                                      record?.wip_details?.total_pending_qty_from_sent_qty ??
                                      (record?.total_inv_qty_for_processing ?? 0) -
                                      (record?.total_grn_qty_for_processing ?? 0);

                                    // Ensure it's always a number and non-negative
                                    const normalizedQty = Math.max(Number(pendingQty) || 0, 0);

                                    const precision = record?.uom_info?.precision ?? 0;
                                    const uqc = record?.uom_info?.uqc?.toProperCase?.() || '';

                                    return `${QUANTITY(normalizedQty, precision)} ${uqc}`;
                                  })()}
                                  &nbsp;
                                  {Number(record?.wip_details?.total_pending_qty_from_sent_qty || 0) > 0 && (
                                    <div onClick={() => {
                                      if (['IN_PROGRESS']?.includes(selectedJobWork?.status)) {
                                        setShowCompleteProcessing(true);
                                        setCurrentProcessingItem(record);
                                        setProcessingCompletionType('PENDING');
                                      } else {
                                        notification.error({
                                          message: 'You can only update processing quantity when the job card is in progress',
                                          placement: 'top',
                                          duration: 4,
                                          type: 'error',
                                        });
                                      }
                                    }}
                                    >
                                      <EditOutlined />
                                    </div>
                                  )}
                                </div>
                              ),
                            },
                            {
                              title: 'Rework',
                              render: (record) => (
                                <div className="flex-display flex-align-c jw-wrapper__material-qty">
                                  {`${QUANTITY(record?.wip_details?.total_rework_qty_from_sent_qty ?? 0, record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`}
                                  &nbsp;
                                  {Number(record?.wip_details?.total_rework_qty_from_sent_qty ?? 0) > 0 && (
                                    <div onClick={() => {
                                      setShowCompleteProcessing(true);
                                      setCurrentProcessingItem(record);
                                      setProcessingCompletionType('REWORK');
                                    }}
                                    >
                                      <EditOutlined />
                                    </div>
                                  )}
                                </div>
                              ),
                            },
                            {
                              title: 'Completed',
                              render: (record) => (
                                <Fragment>
                                  {`${QUANTITY((Number(record?.wip_details?.total_processed_qty_from_sent_qty ?? 0) + Number(record?.total_grn_gen_qty ?? 0) || Number(record?.total_grn_qty_for_processing)), record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`}
                                  <H3Progress percent={Number.parseInt(((Number(record?.wip_details?.total_processed_qty_from_sent_qty ?? 0) + Number(record?.total_grn_gen_qty ?? 0)) || Number(record?.total_grn_qty_for_processing)) / Number(record?.required_processing_qty || 0) * 100)} barWidth="70px" />
                                </Fragment>
                              ),
                            },
                          ]}
                          dataSource={selectedJobWork?.processing_materials}
                          bordered
                        />
                      </div>
                    )
                  }
                  {
                    selectedJobWork?.adhoc_stock_adjustments?.length > 0 && (
                      <Collapse className="view-jobwork__linked-pos">
                        <Panel
                          header={(
                            <span>
                              <FontAwesomeIcon icon={faFileInvoiceDollar} />
                              &nbsp;
                              {`${getStockAdjustmentLines()?.length} manual stock adjustments`}
                            </span>
                          )}
                          key="1"
                        >
                          <Fragment>
                            <Table
                              pagination={false}
                              showHeader={false}
                              size="small"
                              columns={[
                                {
                                  title: '',
                                  render: (record) => <Link to={`/inventory/indent?tenant_id=${selectedJobWork?.tenant_id}&ia&id=${record?.ia_id}`} target="_blank">{record?.ia_number}</Link>,
                                },
                                {
                                  title: '',
                                  render: (record) => (
                                    <Link to={`/inventory/product/view/${record.product_sku_id}`} target="_blank">
                                      {`#${record?.internal_sku_code} - ${record?.product_sku_name}`}
                                    </Link>
                                  ),
                                },
                                {
                                  title: '',
                                  render: (record) => `${record?.quantity} ${record?.uom_info?.uqc?.toProperCase()}`,
                                },
                                {
                                  title: '',
                                  render: (record) => (
                                    <div className="status-tag" style={{ backgroundColor: Helpers.getStatusColor(record?.status)?.color }}>
                                      {Helpers.getStatusColor(record?.status)?.text}
                                    </div>
                                  ),
                                },
                              ]}
                              dataSource={getStockAdjustmentLines()}
                              bordered
                            />
                          </Fragment>
                        </Panel>
                      </Collapse>
                    )
                  }
                  {
                    selectedJobWork?.linked_pos?.filter((i) => ['SENT_FOR_APPROVAL', 'ISSUED', 'DRAFT'].includes(i?.status))?.length > 0 && (
                      <Collapse className="view-jobwork__linked-pos">
                        <Panel
                          header={(
                            <span>
                              <FontAwesomeIcon icon={faFileInvoiceDollar} />
                              &nbsp;
                              {`${selectedJobWork?.linked_pos?.filter((i) => ['SENT_FOR_APPROVAL', 'ISSUED', 'DRAFT'].includes(i?.status))?.length} work orders sent to subcontractor`}
                            </span>
                          )}
                          key="1"
                        >
                          <Fragment>
                            <Table
                              pagination={false}
                              showHeader={false}
                              size="small"
                              columns={[
                                {
                                  title: '',
                                  render: (record) => record?.po_number,
                                },
                                {
                                  title: '',
                                  render: (record) => <H3Text text={Helpers.getStatusColor(record?.status)?.text} className="status-tag" />,
                                },
                                {
                                  title: '',
                                  render: (record) => dayjs(record?.po_date).format('DD-MM-YYYY'),
                                },
                              ]}
                              onRow={(record) => ({
                                onClick: () => window.open(`/approval?type=po&id=${record?.po_id}`, '_blank'),
                              })}
                              dataSource={selectedJobWork?.linked_pos?.filter((i) => ['SENT_FOR_APPROVAL', 'ISSUED', 'DRAFT'].includes(i?.status))}
                              bordered
                            />
                          </Fragment>
                        </Panel>
                      </Collapse>
                    )
                  }
                  {
                    selectedJobWork?.linked_grns?.filter((i) => ['SENT_FOR_APPROVAL', 'ISSUED', 'PENDING_FOR_QC', 'DRAFT'].includes(i?.status))?.length && (
                      <Collapse className="view-jobwork__linked-pos">
                        <Panel
                          header={(
                            <span>
                              <FontAwesomeIcon icon={faCartFlatbed} />
                              &nbsp;
                              {`${selectedJobWork?.linked_grns?.filter((i) => ['SENT_FOR_APPROVAL', 'ISSUED', 'PENDING_FOR_QC', 'DRAFT'].includes(i?.status))?.length} work orders received from subcontractor`}
                            </span>
                          )}
                          key="1"
                        >
                          <Fragment>
                            <Table
                              pagination={false}
                              showHeader={false}
                              size="small"
                              columns={[
                                {
                                  title: '',
                                  render: (record) => (
                                    <Link to={`/purchase/goods-receiving/view/${record?.grn_id}`} target="_blank">
                                      {record?.grn_number}
                                    </Link>
                                  ),
                                },
                                {
                                  title: '',
                                  render: (record) => (
                                    <div className="status-tag" style={{ backgroundColor: Helpers.getStatusColor(record?.status)?.color }}>
                                      {Helpers.getStatusColor(record?.status)?.text}
                                    </div>
                                  ),
                                },
                                {
                                  title: '',
                                  render: (record) => dayjs(record?.grn_date).format('DD-MM-YYYY'),
                                },
                              ]}
                              onRow={(record) => ({
                                onClick: () => window.open(`/purchase/goods-receiving/view/${record?.grn_id}`, '_blank'),
                              })}
                              dataSource={selectedJobWork?.linked_grns?.filter((i) => ['SENT_FOR_APPROVAL', 'PENDING_FOR_QC', 'ISSUED'].includes(i?.status))}
                              bordered
                            />
                          </Fragment>
                        </Panel>
                      </Collapse>
                    )
                  }
                  {
                    selectedJobWork?.linked_invoices?.filter((i) => ['SENT_FOR_APPROVAL', 'CONFIRMED', 'DRAFT'].includes(i?.status))?.length > 0 && (
                      <Collapse className="view-jobwork__linked-pos">
                        <Panel
                          header={(
                            <span>
                              <FontAwesomeIcon icon={faFileInvoiceDollar} />
                              &nbsp;
                              {`${selectedJobWork?.linked_invoices?.filter((i) => ['SENT_FOR_APPROVAL', 'CONFIRMED', 'DRAFT'].includes(i?.status))?.length} raw materials invoices issued to subcontractor`}
                            </span>
                          )}
                          key="1"
                        >
                          <Fragment>
                            <Table
                              pagination={false}
                              showHeader={false}
                              size="small"
                              columns={[
                                {
                                  title: '',
                                  render: (record) => record?.invoice_number,
                                },
                                {
                                  title: '',
                                  render: (record) => (
                                    <div style={{ backgroundColor: Helpers.getStatusColor(record?.status)?.color }} className="status-tag">
                                      {Helpers.getStatusColor(record?.status)?.text}
                                    </div>
                                  ),
                                },
                                {
                                  title: '',
                                  render: (record) => dayjs(record?.invoice_date).format('DD-MM-YYYY'),
                                },
                                {
                                  title: '',
                                  render: (record) => ((isDataMaskingPolicyEnable && isHideSellingPrice) ? <HideValue showPopOver popOverMessage={'You don\'t have access to view amount'} /> : MONEY(record?.invoice_grand_total)),
                                },
                              ]}
                              onRow={(record) => ({
                                onClick: () => window.open(`/sales/invoice/view/${record?.invoice_id}`, '_blank'),
                              })}
                              dataSource={selectedJobWork?.linked_invoices?.filter((i) => ['SENT_FOR_APPROVAL', 'CONFIRMED', 'DRAFT'].includes(i?.status))}
                              bordered
                            />
                          </Fragment>
                        </Panel>
                      </Collapse>
                    )
                  }
                </div>
                {
                  selectedJobWork?.incomplete_previous_job_works?.length > 0 && Number(selectedJobWork?.pending_qty) <= 0 && selectedJobWork?.status !== 'COMPLETED' && (
                    <Collapse className="view-jobwork__blocking-jw">
                      <Panel
                        header={(
                          <span>
                            <FontAwesomeIcon icon={faTriangleExclamation} />
                            &nbsp;
                            {`This Job Card is blocked on ${selectedJobWork?.incomplete_previous_job_works?.length} other job cards`}
                          </span>
                        )}
                        key="1"
                      >
                        <Fragment>
                          <Table
                            pagination={false}
                            showHeader={false}
                            size="small"
                            columns={[
                              {
                                title: '',
                                dataIndex: 'jw_number',
                              },
                              {
                                title: '',
                                dataIndex: 'process_name',
                              },
                              {
                                title: '',
                                render: (record) => (
                                  <div
                                    className="status-tag"
                                    style={{ backgroundColor: getStatusColor(record?.status)?.color }}
                                  >
                                    {getStatusColor(record?.status)?.text}
                                  </div>
                                ),
                              },
                            ]}
                            dataSource={selectedJobWork?.incomplete_previous_job_works}
                            bordered
                          />
                        </Fragment>
                      </Panel>
                    </Collapse>
                  )
                }
              </div>
            </div>
          </div>
        </div>
      )}
      <Drawer
        onClose={() => setShowQuantityUpdate(false)}
        open={showQuantityUpdate}
        width="440px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '395px' }}>
            <H3Text text={`Update ${updateType?.toProperCase()} Quantity`} className="custom-drawer__title" />
            <H3Image src={cdnUrl("icon-close-blue.png", "icons")} className="custom-drawer__close-icon" onClick={() => setShowQuantityUpdate(false)} />
          </div>
        </div>
        <JwQuantityUpdate
          updateType={updateType}
          quantity={targetQuantity}
          uomInfo={targetUomInfo}
          selectedJobWork={selectedJobWork}
          callback={() => {
            getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
            if (callback) {
              getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
              callback();
            }
            setTargetQuantity('');
            setTargetUomInfo(null);
            setShowQuantityUpdate(false);
            setUpdateType('');
          }}
          selectedOutputMaterial={selectedOutputMaterial}
        />
      </Drawer>
      <Drawer
        onClose={() => setShowIssueRmDrawer(false)}
        open={showIssueRmDrawer}
        width="940px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '895px' }}>
            <H3Text text={`Issue Required Raw Material for ${selectedJobWork?.process_name}`} className="custom-drawer__title" />
            <H3Image src={cdnUrl("icon-close-blue.png", "icons")} className="custom-drawer__close-icon" onClick={() => setShowIssueRmDrawer(false)} />
          </div>
        </div>
        <div className="view-jw__rm-drawer">
          {
            selectedJobWork?.subcontractor_seller_id ? (
              <InvoiceForm
                tenantId={selectedJobWork?.tenant_id}
                tenantDepartmentId={selectedJobWork?.tenant_department_info?.tenant_department_id}
                jwRmLines={getInvoiceLinesFromJobWorks([selectedJobWork], rmIssueType === 'RAW_MATERIAL' ? 'INPUT' : 'PROCESSING')}
                jwCallback={() => {
                  getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
                  if (callback) {
                    callback();
                  }
                  setShowIssueRmDrawer(false);
                }}
                isCarryForward={true}
              />
            ) : (
              <IssueRawMaterial
                selectedJobWork={selectedJobWork}
                rmIssueType={rmIssueType}
                items={rmIssueType === 'RAW_MATERIAL' ? selectedJobWork?.input_materials : selectedJobWork?.processing_materials}
                callback={() => {
                  getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
                  if (callback) {
                    callback();
                  }
                  setShowIssueRmDrawer(false);
                }}
              />
            )
          }
        </div>
      </Drawer>
      <Drawer
        onClose={() => setShowCreatePoDrawer(false)}
        open={showCreatePoDrawer}
        width="1100px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '1050px' }}>
            <H3Text text="Send Work Order" className="custom-drawer__title" />
            <H3Image
              src={cdnUrl("icon-close-blue.png", "icons")}
              className="custom-drawer__close-icon"
              onClick={() => setShowCreatePoDrawer(false)}
            />
          </div>
        </div>
        <POForm
          tenantId={selectedJobWork?.tenant_id}
          jwInfo={getPoLinesFromJobWorks([selectedJobWork], rmIssueType === 'RAW_MATERIAL' ? 'INPUT' : 'PROCESSING')}
          moCallback={() => {
            if (callback) {
              callback();
            }
            getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
            setShowCreatePoDrawer(false);
          }}
        />
      </Drawer>
      <Drawer
        onClose={() => setShowCompleteProcessing(false)}
        open={showCompleteProcessing}
        width="960px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '905px' }}>
            <H3Text text={`Complete processing of materials for job card - ${selectedJobWork?.process_name}`} className="custom-drawer__title" />
            <H3Image
              src={cdnUrl("icon-close-blue.png", "icons")}
              className="custom-drawer__close-icon"
              onClick={() => setShowCompleteProcessing(false)}
            />
          </div>
        </div>
        <CompleteProcessing
          selectedJobWork={selectedJobWork}
          items={[currentProcessingItem]}
          processingCompletionType={processingCompletionType}
          callback={() => {
            getJobWorkById({ tenantId: selectedTenant, productionRouteLineId: jobworkId });
            if (callback) {
              callback();
            }
            setShowCompleteProcessing(false);
          }}
        />
      </Drawer>
      <Drawer
        onClose={() => setShowManualAdjustmentDrawer(false)}
        open={showManualAdjustmentDrawer}
        width="960px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '905px' }}>
            <H3Text text={`Create manual adjustments of materials for job card - ${selectedJobWork?.process_name}`} className="custom-drawer__title" />
            <H3Image
              src={cdnUrl("icon-close-blue.png", "icons")}
              className="custom-drawer__close-icon"
              onClick={() => setShowManualAdjustmentDrawer(false)}
            />
          </div>
        </div>
        <div className="view-jw__indent-drawer">
          {/* <CreateIndent selectedJobWork={selectedJobWork} /> */}
          <AdjustmentForm selectedJobWork={selectedJobWork} />
        </div>
      </Drawer>
      <PRZModal
        isOpen={showPRZModal}
        onClose={() => {
          setShowPRZModal(false);
          setSelectedDocumentId(null);
        }}
        entityType="product"
        documentId={selectedDocumentId}
        customStyle={{ zIndex: '1001' }}
      />
    </Fragment>
  );
}

const mapStateToProps = ({
  UserReducers, DepartmentReducers, JobWorkReducers, TemplateRouteReducers, TenantReducers, GetOperatorList,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  departments: DepartmentReducers.departments,
  jobWorks: JobWorkReducers.jobWorks,
  updateJobWorkV2Loading: JobWorkReducers.updateJobWorkV2Loading,
  getJobWorksLoading: JobWorkReducers.getJobWorksLoading,
  updateTemplateRouteStatusLoading: TemplateRouteReducers.updateTemplateRouteStatusLoading,
  updateAssigneeLoading: TemplateRouteReducers.updateAssigneeLoading,
  getJobOperatorsLoading: JobWorkReducers.getJobOperatorsLoading,
  jobOperators: JobWorkReducers.jobOperators,
  orgUsers: TenantReducers.orgUsers,
  selectedJobWork: JobWorkReducers.selectedJobWork,
  getJobWorkByIdLoading: JobWorkReducers.getJobWorkByIdLoading,
  priceMasking: UserReducers.priceMasking,
  operatorList: GetOperatorList?.data?.operators,
  getOperatorListLoading: GetOperatorList.loading,
});

const mapDispatchToProps = (dispatch) => ({
  getJobWorkById: (payload, callback) => dispatch(JobWorkActions.getJobWorkById(payload, callback)),
  updateJobWorkV2: (payload, callback) => dispatch(JobWorkActions.updateJobWorkV2(payload, callback)),
  getJobOperators: (payload, callback) => dispatch(JobWorkActions.getJobOperators(payload, callback)),
  updateTemplateRouteStatus: (payload, callback) => dispatch(TemplateRouteActions.updateTemplateRouteStatus(payload, callback)),
  updateAssignee: (payload, callback) => dispatch(TemplateRouteActions.updateAssignee(payload, callback)),
  quickUpdateEntityWise: (payload, Callback, rollback) => dispatch(quickUpdateActions.quickUpdateEntityWise(payload, Callback, rollback)),
  downloadDocument: (payload, document) => dispatch(AnalyticsActions.downloadDocument(payload, document)),
  getOperatorList: (payload, callback) => dispatch(GetOperatorList.actions.request(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ViewJobWork));
